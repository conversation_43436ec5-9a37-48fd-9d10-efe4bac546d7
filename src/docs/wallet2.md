```ts
// CLEAN ARCHITECTURE: Separate financial data from user collection

// user.model.ts - NO FINANCIAL FIELDS
export interface IUser {
  _id: string
  name: string
  email: string
  // ... only user profile data, NO financial fields
}

// wallet-balances.model.ts - SOURCE OF TRUTH FOR MONEY
export interface WalletBalance {
  uid: string // User or School ID
  isSchool: boolean // True if uid refers to school
  balanceType: 'giftCard' | 'commission' | 'earnings' // Type of balance
  availableBalance: number // Available balance in cents
  reservedBalance: number // Currently reserved amount in cents
  totalBalance: number // available + reserved (computed field)
  lastTransactionId?: string // Last transaction that affected this balance
  lastTransactionDate?: Date // When last transaction occurred
  version: number // For optimistic locking
}

export enum WalletBalanceType {
  GIFT_CARD = 'giftCard',
  COMMISSION = 'commission',
  EARNINGS = 'earnings',
}

const walletBalanceSchema = new Schema<IWalletBalance>(
  {
    uid: {type: String, required: true, index: true},
    isSchool: {type: Boolean, default: false, index: true},
    balanceType: {
      type: String,
      enum: Object.values(WalletBalanceType),
      required: true,
      index: true,
    },
    availableBalance: {type: Number, required: true, default: 0},
    reservedBalance: {type: Number, required: true, default: 0},
    totalBalance: {
      type: Number,
      required: true,
      default: 0,
      // Virtual field that auto-calculates
      set: function () {
        return this.availableBalance + this.reservedBalance
      },
    },
    lastTransactionId: {type: String, index: true},
    lastTransactionDate: {type: Date, index: true},
    version: {type: Number, default: 0},
  },
  {
    timestamps: true,
  }
)

// Compound unique index
walletBalanceSchema.index(
  {
    uid: 1,
    balanceType: 1,
  },
  {
    unique: true,
    name: 'uid_balanceType_unique',
  }
)

export const WalletBalance = mongoose.model('WalletBalance', walletBalanceSchema)

// wallet-transactions.model.ts - AUDIT TRAIL
export interface IWalletTransaction {
  _id: string
  uid: string
  balanceType: WalletBalanceType
  transactionType: TransactionType
  amount: number // Can be positive or negative
  orderId?: string // Associated order if applicable
  description: string
  balanceAfter: number
  reservedAfter: number
  metadata?: any
}

export enum TransactionType {
  RESERVE = 'reserve',
  CONFIRM = 'confirm',
  RELEASE = 'release',
  TOP_UP = 'topup',
  ADMIN_ADJUSTMENT = 'admin_adjustment',
  PAYOUT = 'payout',
  REFUND = 'refund',
}

const walletTransactionSchema = new Schema<IWalletTransaction>(
  {
    uid: {type: String, required: true, index: true},
    balanceType: {
      type: String,
      enum: Object.values(WalletBalanceType),
      required: true,
      index: true,
    },
    transactionType: {
      type: String,
      enum: Object.values(TransactionType),
      required: true,
      index: true,
    },
    amount: {type: Number, required: true},
    orderId: {type: String, index: true},
    description: {type: String, required: true},
    balanceAfter: {type: Number, required: true},
    reservedAfter: {type: Number, required: true},
    metadata: {type: Schema.Types.Mixed},
  },
  {
    timestamps: true,
  }
)

walletTransactionSchema.index({uid: 1, createdAt: -1})
walletTransactionSchema.index({orderId: 1})

export const WalletTransaction = mongoose.model('WalletTransaction', walletTransactionSchema)

// WALLET SERVICE - HANDLES ALL FINANCIAL OPERATIONS
export class WalletService {
  // Initialize wallet for new user
  static async initializeWallet(uid: string, isSchool: boolean = false): Promise<void> {
    const balanceTypes = Object.values(WalletBalanceType)

    const walletBalances = balanceTypes.map((balanceType) => ({
      uid,
      isSchool,
      balanceType,
      availableBalance: 0,
      reservedBalance: 0,
      totalBalance: 0,
      version: 0,
    }))

    await WalletBalance.insertMany(walletBalances, {ordered: false})
  }

  // Reserve gift card balance (atomic operation)
  static async reserveGiftCardBalance(uid: string, amount: number, orderId: string): Promise<boolean> {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        // Find and update with optimistic locking
        const wallet = await WalletBalance.findOne({
          uid,
          balanceType: WalletBalanceType.GIFT_CARD,
        }).session(session)

        if (!wallet || wallet.availableBalance < amount) {
          throw new Error('Insufficient gift card balance')
        }

        // Atomic update with version check
        const result = await WalletBalance.findOneAndUpdate(
          {
            uid,
            balanceType: WalletBalanceType.GIFT_CARD,
            version: wallet.version, // Optimistic locking
          },
          {
            $inc: {
              availableBalance: -amount,
              reservedBalance: amount,
              version: 1,
            },
            $set: {
              totalBalance: wallet.totalBalance, // Remains same
              lastTransactionDate: new Date(),
            },
          },
          {
            session,
            new: true,
            runValidators: true,
          }
        )

        if (!result) {
          throw new Error('Concurrent modification detected, please retry')
        }

        // Create transaction record
        await WalletTransaction.create(
          [
            {
              uid,
              balanceType: WalletBalanceType.GIFT_CARD,
              transactionType: TransactionType.RESERVE,
              amount: -amount,
              orderId,
              description: `Reserved ${amount / 100} for order ${orderId}`,
              balanceAfter: result.availableBalance,
              reservedAfter: result.reservedBalance,
            },
          ],
          {session}
        )

        return true
      })
    } catch (error) {
      console.error('Reserve balance failed:', error)
      return false
    } finally {
      await session.endSession()
    }
  }

  // Confirm gift card usage (order successful)
  static async confirmGiftCardUsage(uid: string, amount: number, orderId: string): Promise<boolean> {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        const wallet = await WalletBalance.findOne({
          uid,
          balanceType: WalletBalanceType.GIFT_CARD,
        }).session(session)

        if (!wallet || wallet.reservedBalance < amount) {
          throw new Error('Invalid reserved balance')
        }

        const result = await WalletBalance.findOneAndUpdate(
          {
            uid,
            balanceType: WalletBalanceType.GIFT_CARD,
            version: wallet.version,
          },
          {
            $inc: {
              reservedBalance: -amount,
              version: 1,
            },
            $set: {
              totalBalance: wallet.totalBalance - amount,
              lastTransactionDate: new Date(),
            },
          },
          {session, new: true}
        )

        if (!result) {
          throw new Error('Concurrent modification detected')
        }

        await WalletTransaction.create(
          [
            {
              uid,
              balanceType: WalletBalanceType.GIFT_CARD,
              transactionType: TransactionType.CONFIRM,
              amount: -amount,
              orderId,
              description: `Confirmed usage of ${amount / 100} for order ${orderId}`,
              balanceAfter: result.availableBalance,
              reservedAfter: result.reservedBalance,
            },
          ],
          {session}
        )

        return true
      })
    } catch (error) {
      console.error('Confirm usage failed:', error)
      return false
    } finally {
      await session.endSession()
    }
  }

  // Release reserved balance (order failed/cancelled)
  static async releaseGiftCardReservation(uid: string, amount: number, orderId: string): Promise<boolean> {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        const wallet = await WalletBalance.findOne({
          uid,
          balanceType: WalletBalanceType.GIFT_CARD,
        }).session(session)

        if (!wallet) {
          throw new Error('Wallet not found')
        }

        const result = await WalletBalance.findOneAndUpdate(
          {
            uid,
            balanceType: WalletBalanceType.GIFT_CARD,
            version: wallet.version,
          },
          {
            $inc: {
              availableBalance: amount,
              reservedBalance: -amount,
              version: 1,
            },
            $set: {
              lastTransactionDate: new Date(),
            },
          },
          {session, new: true}
        )

        if (!result) {
          throw new Error('Concurrent modification detected')
        }

        await WalletTransaction.create(
          [
            {
              uid,
              balanceType: WalletBalanceType.GIFT_CARD,
              transactionType: TransactionType.RELEASE,
              amount: amount,
              orderId,
              description: `Released ${amount / 100} from cancelled order ${orderId}`,
              balanceAfter: result.availableBalance,
              reservedAfter: result.reservedBalance,
            },
          ],
          {session}
        )

        return true
      })
    } catch (error) {
      console.error('Release reservation failed:', error)
      return false
    } finally {
      await session.endSession()
    }
  }

  // Top up gift card balance
  static async topUpGiftCard(uid: string, amount: number, description: string = 'Gift card top up'): Promise<boolean> {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        const result = await WalletBalance.findOneAndUpdate(
          {
            uid,
            balanceType: WalletBalanceType.GIFT_CARD,
          },
          {
            $inc: {
              availableBalance: amount,
              totalBalance: amount,
              version: 1,
            },
            $set: {
              lastTransactionDate: new Date(),
            },
          },
          {
            session,
            new: true,
            upsert: true, // Create if doesn't exist
          }
        )

        await WalletTransaction.create(
          [
            {
              uid,
              balanceType: WalletBalanceType.GIFT_CARD,
              transactionType: TransactionType.TOP_UP,
              amount: amount,
              description,
              balanceAfter: result.availableBalance,
              reservedAfter: result.reservedBalance,
            },
          ],
          {session}
        )

        return true
      })
    } catch (error) {
      console.error('Top up failed:', error)
      return false
    } finally {
      await session.endSession()
    }
  }

  // Get user's complete wallet summary
  static async getWalletSummary(uid: string): Promise<Record<string, any>> {
    const wallets = await WalletBalance.find({uid})

    return wallets.reduce((summary, wallet) => {
      summary[wallet.balanceType] = {
        available: wallet.availableBalance,
        reserved: wallet.reservedBalance,
        total: wallet.totalBalance,
      }
      return summary
    }, {} as Record<string, any>)
  }

  // Get specific balance type
  static async getBalance(uid: string, balanceType: WalletBalanceType): Promise<IWalletBalance | null> {
    return WalletBalance.findOne({uid, balanceType})
  }

  // Get transaction history
  static async getTransactionHistory(uid: string, balanceType?: WalletBalanceType, limit: number = 50) {
    const query: any = {uid}
    if (balanceType) query.balanceType = balanceType

    return WalletTransaction.find(query).sort({createdAt: -1}).limit(limit)
  }
}

// UPDATED BALANCE SNAPSHOTS - NOW PURELY CACHE
export class BalanceSnapshotService {
  // Calculate gift card balance from wallet-balances (source of truth)
  static async calculateGiftCardBalance(uid: string, isSchool: boolean): Promise<void> {
    const wallet = await WalletBalance.findOne({
      uid,
      balanceType: WalletBalanceType.GIFT_CARD,
    })

    if (!wallet) return

    // Cache the available balance for quick dashboard access
    await this.upsertSnapshot({
      uid,
      isSchool,
      balanceType: BalanceType.GIFT_CARDS,
      balance: wallet.availableBalance,
      metadata: {
        reservedBalance: wallet.reservedBalance,
        totalBalance: wallet.totalBalance,
        lastTransactionDate: wallet.lastTransactionDate,
        source: 'wallet-balances', // Indicate source
      },
    })
  }

  // Recalculate all financial balances from their respective sources
  static async recalculateUserBalances(uid: string, isSchool: boolean = false): Promise<void> {
    await Promise.allSettled([
      this.calculateIncomeBalance(uid, isSchool), // From income-logs
      this.calculateGiftCardBalance(uid, isSchool), // From wallet-balances
      this.calculateCommissionBalance(uid, isSchool), // From income-logs
      this.calculatePendingPayoutBalance(uid, isSchool), // From income-logs + payout-logs
      // ... other balance types
    ])
  }
}

// ORDER SERVICE - UPDATED
export class OrderService {
  static async createOrder(orderData: {userId: string; items: any[]; giftCardAmount?: number; totalAmount: number}) {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        // 1. Create order
        const order = await Order.create(
          [
            {
              buyer: orderData.userId,
              items: orderData.items,
              totalAmount: orderData.totalAmount,
              giftCardAmount: orderData.giftCardAmount || 0,
              status: 'pending',
            },
          ],
          {session}
        )

        // 2. Reserve gift card balance if used
        if (orderData.giftCardAmount && orderData.giftCardAmount > 0) {
          const reserveSuccess = await WalletService.reserveGiftCardBalance(orderData.userId, orderData.giftCardAmount, order[0]._id.toString())

          if (!reserveSuccess) {
            throw new Error('Failed to reserve gift card balance')
          }
        }

        return order[0]
      })
    } finally {
      await session.endSession()
    }
  }

  // Order success
  static async confirmOrder(orderId: string) {
    const order = await Order.findById(orderId)

    if (order && order.giftCardAmount > 0) {
      await WalletService.confirmGiftCardUsage(order.buyer, order.giftCardAmount, orderId)
    }

    await Order.findByIdAndUpdate(orderId, {status: 'completed'})
  }

  // Order failure
  static async cancelOrder(orderId: string) {
    const order = await Order.findById(orderId)

    if (order && order.giftCardAmount > 0) {
      await WalletService.releaseGiftCardReservation(order.buyer, order.giftCardAmount, orderId)
    }

    await Order.findByIdAndUpdate(orderId, {status: 'cancelled'})
  }
}

// API ENDPOINTS
export class WalletController {
  static async getWalletSummary(req: Request, res: Response) {
    try {
      const userId = req.user.id
      const summary = await WalletService.getWalletSummary(userId)

      res.json({success: true, data: summary})
    } catch (error) {
      res.status(500).json({success: false, error: error.message})
    }
  }

  static async getTransactionHistory(req: Request, res: Response) {
    try {
      const userId = req.user.id
      const {balanceType, limit = 50} = req.query

      const transactions = await WalletService.getTransactionHistory(userId, balanceType as WalletBalanceType, Number(limit))

      res.json({success: true, data: transactions})
    } catch (error) {
      res.status(500).json({success: false, error: error.message})
    }
  }
}

// CLEANUP JOB
export const cleanupStuckReservations = async () => {
  const cutoffTime = new Date(Date.now() - 30 * 60 * 1000) // 30 minutes

  const stuckOrders = await Order.find({
    status: 'pending',
    giftCardAmount: {$gt: 0},
    createdAt: {$lt: cutoffTime},
  })

  for (const order of stuckOrders) {
    await WalletService.releaseGiftCardReservation(order.buyer, order.giftCardAmount, order._id.toString())

    await Order.findByIdAndUpdate(order._id, {status: 'expired'})
  }
}

export {WalletService, WalletBalance, WalletTransaction}
```
