### ACL

order document

- buyer: users.\_id or school-plan.\_id
- isSchool: true if buyer is school-plan.\_id

income-log

- uid: users.\_id or school-plan.\_id
- isSchool: true if buyer is school-plan.\_id

so if users.\_id we simply check equality but for school-plan.\_id we need to check if user is admin of that school.
Now I want to implement access control in find and get methods. I am using feathersjs. How to have a common middleware hook, to acheive it.

## 18-25 Aug

- Merge frontend gift cards & service-task - Wed ✅
- Order cancel optimize logic - Wed ✅
- AT Ticket unclaim - Wed ✅
- AT Ticket cancel - Wed ✅ [Already handled]
- AT Order cancel - Wed ✅
  - refund calc ✅
  - 30 days refund logic ✅
  - credit deduction ✅
  - 14 days logic - after that only cancel associated task ✅ [Already handled]
- Batch Logic in Order refund
- Batch Logic in Ticket refund
  ***
- Separate Lecture Cancel - Thu ✅ [Already handled]
- First session free on order cancellation - Sat ✅
- Ticket cancel restrict if `uid` ✅
- Remove ticket and order cancellation linking ✅
- Testing & Bugfixes - Fri - Sat

## 26 Aug, Tue

- test lecture package cancellation/refund ✅
- test lecture package cancellation/refund in school
- Make frontend ready for deployment with backend old version apis

## 27 Aug, Wed (Prashant)

- Test the deployable frontend with new backend

## 28 Aug, Thu (All)

- Merge frontend to master
- Merge backend to master

### Misc

- Associate task orders shoould not be purchased using points
- AT Ticket claim timeout issue
- service pack apply check

* noDiscount case. Maybe only for service_premium? Action: Removed
* is item.isOnCampus for 1v1 mentoring also? Ans: only for substitute
* total=10, used=2, display: 10-2/10 = 8/10
* refund=8, total=10-8=2, used=2, display: 2-2/2 = 0/2

### Demo Session Refund

- First order M1, (pack.\_id of M1)
- First order of Service Package S1, take L1 (pack.\_id of S1)

### Deployment strategy

- Backend dependent on frontend
  - order status 110 -> processing conditions
    - need to deploy web first with:
      - order details 110 flow
      - Sys order dispute
  - ticket and lecture cancellation separated ✅
    - solution v2 api ✅

### Reason

1. First backend - will be deployed

- if we deploy frontend, frontend new features will break

2. Due to backend deployment. - issue in frontend

3. Minor changes in frontend - so that it doesnt break due to backend deployment

Actual Deployment Stages:

1. Minor Frontend changes: current backend + minor frontend changes
2. Backend Deployment: back end changes + minor frontend changes
3. Major Frontend changes after testing in dev environment

back end changes + minor frontend changes

### Frontend Changes to deploy so that after backend deployment, frontend will not break:

1. Payment Confirmation Page(after payment is done)

- order processing state(status: 110)
  - Show users: 'Please wait while we process your order...'

2. Order Details Page

- order processing state(status: 110)
  - Show users: 'Please wait while we process your order...'

3. Added new page in sys for dev only

- To track failed order/refund status/transaction conflict errors

**Without point 1 & 2, order/payment confirmation pages will break on order status 110**

Here’s a simplified and shorter version:

---

### Required Frontend Changes before Backend Deployment

1. **Payment Confirmation Page**

   - Show: _“Please wait while we process your order...”_ when status = 110

2. **Order Details Page**

   - Show: _“Please wait while we process your order...”_ when status = 110

3. **New Dev-Only Page** in sys

   - Track failed orders, refunds, and transaction write conflicts if any
   - This page is purely for me to see how transaction feature is going, how many orders are failing daily etc.

⚠️ Without (1) & (2), those pages will break on status 110.

---

Want me to make it even more concise, like a one-liner changelog entry?
