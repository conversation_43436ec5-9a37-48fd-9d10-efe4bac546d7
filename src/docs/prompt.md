1. in @order.hooks.ts, instead of fetching balance from const account = await d.app.service(modelName).Model.findOne({\_id: buyer}).lean() we need to use wallet-balance(giftCard balance)

2. In gift-card-log.class.ts, in createGiftCardLog we need to use wallet-balance(giftCard balance) and update it accordingly instead of users/school-plan.

3. We can shift releaseReservedBalance from gift-card-log.service.ts to wallet-balance.service.ts, then update @order.class.ts to use wallet-balance.releaseReservedBalance instead of gift-card-log.releaseReservedBalance

---

I am confused which service to use when or how to structure them. Because gift-card-logs(or maybe income-logs etc.) and wallet-balance are dependent on each other. When we reserve a balance then we are creating gift-card-log, then when we releaseReservedBalance we need to add a new gift-card-log. But no log should be created during deductReservedBalance. Also in wallet-balance transactionId we need to use the same \_id from gift-card-log created. We can do new ObjectId() and use in both places.
