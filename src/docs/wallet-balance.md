```ts
export interface WalletBalance {
  uid: string // User or School ID
  isSchool: boolean // True if uid refers to school
  balanceType: 'giftCard' | 'commission' | 'earnings' // Type of balance
  availableBalance: number // Available balance in cents
  reservedBalance: number // Currently reserved amount in cents
  totalBalance: number // available + reserved (computed field)
  lastTransactionId?: string // Last transaction that affected this balance
  lastTransactionDate?: Date // When last transaction occurred
  version: number // For optimistic locking
}

// Enhanced Schema Design for Giftcard Reservations

// 1. Enhanced Wallet Balances Collection
const walletBalanceSchema = {
  _id: ObjectId,
  userId: ObjectId,
  balanceType: String, // 'income', 'commission', 'giftcard'
  totalBalance: Number, // Total balance including reserved
  availableBalance: Number, // Available for new reservations
  reservedBalance: Number, // Currently reserved amount
  lastTransactionId: ObjectId,
  lastUpdated: Date,
  version: Number,

  // Indexes
  // { userId: 1, balanceType: 1 } - unique
  // { lastUpdated: 1 }
}

// 2. Enhanced Transaction Schema with Reservation Support
const transactionSchema = {
  _id: ObjectId,
  userId: ObjectId,
  balanceType: String,
  type: String, // 'credit', 'debit', 'reserve', 'release', 'confirm'
  amount: Number,
  status: String, // 'pending', 'confirmed', 'failed', 'expired'
  description: String,
  referenceId: String, // Order ID for reservations
  reservationId: ObjectId, // For linking reserve/release/confirm transactions
  expiresAt: Date, // For automatic cleanup of expired reservations
  metadata: Object,
  totalBalanceAfter: Number,
  availableBalanceAfter: Number,
  reservedBalanceAfter: Number,
  createdAt: Date,
  confirmedAt: Date,

  // Indexes
  // { userId: 1, balanceType: 1, createdAt: -1 }
  // { status: 1, expiresAt: 1 } - for cleanup jobs
  // { referenceId: 1 }
  // { reservationId: 1 }
}

// 3. Enhanced Wallet Service with Reservation Logic
class EnhancedWalletService {
  // Get available balance (what user can actually spend)
  async getAvailableBalance(userId, balanceType = 'giftcard') {
    const wallet = await db.collection('wallet_balances').findOne({
      userId: ObjectId(userId),
      balanceType,
    })

    return wallet ? wallet.availableBalance : 0
  }

  // Get all balance details
  async getBalanceDetails(userId, balanceType = 'giftcard') {
    const wallet = await db.collection('wallet_balances').findOne({
      userId: ObjectId(userId),
      balanceType,
    })

    if (!wallet) {
      return {
        totalBalance: 0,
        availableBalance: 0,
        reservedBalance: 0,
      }
    }

    return {
      totalBalance: wallet.totalBalance,
      availableBalance: wallet.availableBalance,
      reservedBalance: wallet.reservedBalance,
    }
  }

  // Reserve giftcard balance for order
  async reserveBalance(userId, amount, orderId, expirationMinutes = 30) {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        // 1. Check available balance with lock
        const wallet = await db.collection('wallet_balances').findOne(
          {
            userId: ObjectId(userId),
            balanceType: 'giftcard',
          },
          {session}
        )

        if (!wallet || wallet.availableBalance < amount) {
          throw new Error('Insufficient available balance')
        }

        // 2. Create reservation transaction
        const reservationId = new ObjectId()
        const expiresAt = new Date(Date.now() + expirationMinutes * 60 * 1000)

        const transaction = {
          _id: reservationId,
          userId: ObjectId(userId),
          balanceType: 'giftcard',
          type: 'reserve',
          amount: amount,
          status: 'pending',
          description: `Reserve for order ${orderId}`,
          referenceId: orderId,
          reservationId: reservationId,
          expiresAt: expiresAt,
          metadata: {orderId},
          totalBalanceAfter: wallet.totalBalance, // No change to total
          availableBalanceAfter: wallet.availableBalance - amount,
          reservedBalanceAfter: wallet.reservedBalance + amount,
          createdAt: new Date(),
        }

        // 3. Update wallet balances
        const updateResult = await db.collection('wallet_balances').updateOne(
          {
            userId: ObjectId(userId),
            balanceType: 'giftcard',
            version: wallet.version,
            availableBalance: {$gte: amount}, // Double-check availability
          },
          {
            $set: {
              availableBalance: wallet.availableBalance - amount,
              reservedBalance: wallet.reservedBalance + amount,
              lastUpdated: new Date(),
              lastTransactionId: reservationId,
            },
            $inc: {version: 1},
          },
          {session}
        )

        if (updateResult.modifiedCount === 0) {
          throw new Error('Failed to reserve balance - insufficient funds or concurrent modification')
        }

        // 4. Insert transaction
        await db.collection('transactions').insertOne(transaction, {session})

        return {
          reservationId: reservationId,
          reservedAmount: amount,
          expiresAt: expiresAt,
          availableBalance: wallet.availableBalance - amount,
        }
      })
    } finally {
      await session.endSession()
    }
  }

  // Confirm reservation (when payment succeeds)
  async confirmReservation(reservationId, orderId) {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        // 1. Find the reservation transaction
        const reservation = await db.collection('transactions').findOne(
          {
            _id: ObjectId(reservationId),
            type: 'reserve',
            status: 'pending',
            referenceId: orderId,
          },
          {session}
        )

        if (!reservation) {
          throw new Error('Reservation not found or already processed')
        }

        if (reservation.expiresAt < new Date()) {
          throw new Error('Reservation has expired')
        }

        // 2. Get current wallet state
        const wallet = await db.collection('wallet_balances').findOne(
          {
            userId: reservation.userId,
            balanceType: 'giftcard',
          },
          {session}
        )

        if (!wallet) {
          throw new Error('Wallet not found')
        }

        // 3. Create confirmation transaction
        const confirmTransaction = {
          userId: reservation.userId,
          balanceType: 'giftcard',
          type: 'confirm',
          amount: reservation.amount,
          status: 'confirmed',
          description: `Confirm purchase for order ${orderId}`,
          referenceId: orderId,
          reservationId: ObjectId(reservationId),
          metadata: {orderId},
          totalBalanceAfter: wallet.totalBalance - reservation.amount,
          availableBalanceAfter: wallet.availableBalance, // No change to available
          reservedBalanceAfter: wallet.reservedBalance - reservation.amount,
          createdAt: new Date(),
          confirmedAt: new Date(),
        }

        // 4. Update wallet balances
        await db.collection('wallet_balances').updateOne(
          {
            userId: reservation.userId,
            balanceType: 'giftcard',
          },
          {
            $set: {
              totalBalance: wallet.totalBalance - reservation.amount,
              reservedBalance: wallet.reservedBalance - reservation.amount,
              lastUpdated: new Date(),
            },
            $inc: {version: 1},
          },
          {session}
        )

        // 5. Update reservation status and insert confirmation
        await db.collection('transactions').updateOne(
          {_id: ObjectId(reservationId)},
          {
            $set: {
              status: 'confirmed',
              confirmedAt: new Date(),
            },
          },
          {session}
        )

        await db.collection('transactions').insertOne(confirmTransaction, {session})

        return {
          confirmed: true,
          deductedAmount: reservation.amount,
          newTotalBalance: wallet.totalBalance - reservation.amount,
          newAvailableBalance: wallet.availableBalance,
        }
      })
    } finally {
      await session.endSession()
    }
  }

  // Release reservation (when order fails or is cancelled)
  async releaseReservation(reservationId, orderId, reason = 'Order cancelled') {
    const session = await mongoose.startSession()

    try {
      return await session.withTransaction(async () => {
        // 1. Find the reservation
        const reservation = await db.collection('transactions').findOne(
          {
            _id: ObjectId(reservationId),
            type: 'reserve',
            status: 'pending',
            referenceId: orderId,
          },
          {session}
        )

        if (!reservation) {
          // Already processed or doesn't exist
          return {released: false, reason: 'Reservation not found or already processed'}
        }

        // 2. Get current wallet state
        const wallet = await db.collection('wallet_balances').findOne(
          {
            userId: reservation.userId,
            balanceType: 'giftcard',
          },
          {session}
        )

        // 3. Create release transaction
        const releaseTransaction = {
          userId: reservation.userId,
          balanceType: 'giftcard',
          type: 'release',
          amount: reservation.amount,
          status: 'confirmed',
          description: `Release reservation for order ${orderId} - ${reason}`,
          referenceId: orderId,
          reservationId: ObjectId(reservationId),
          metadata: {orderId, reason},
          totalBalanceAfter: wallet.totalBalance, // No change to total
          availableBalanceAfter: wallet.availableBalance + reservation.amount,
          reservedBalanceAfter: wallet.reservedBalance - reservation.amount,
          createdAt: new Date(),
          confirmedAt: new Date(),
        }

        // 4. Update wallet balances
        await db.collection('wallet_balances').updateOne(
          {
            userId: reservation.userId,
            balanceType: 'giftcard',
          },
          {
            $set: {
              availableBalance: wallet.availableBalance + reservation.amount,
              reservedBalance: wallet.reservedBalance - reservation.amount,
              lastUpdated: new Date(),
            },
            $inc: {version: 1},
          },
          {session}
        )

        // 5. Update reservation status and insert release
        await db.collection('transactions').updateOne(
          {_id: ObjectId(reservationId)},
          {
            $set: {
              status: 'failed',
              confirmedAt: new Date(),
            },
          },
          {session}
        )

        await db.collection('transactions').insertOne(releaseTransaction, {session})

        return {
          released: true,
          releasedAmount: reservation.amount,
          newAvailableBalance: wallet.availableBalance + reservation.amount,
        }
      })
    } finally {
      await session.endSession()
    }
  }

  // Cleanup expired reservations (run periodically)
  async cleanupExpiredReservations() {
    const expiredReservations = await db
      .collection('transactions')
      .find({
        type: 'reserve',
        status: 'pending',
        expiresAt: {$lt: new Date()},
      })
      .toArray()

    const results = []
    for (const reservation of expiredReservations) {
      try {
        const result = await this.releaseReservation(reservation._id, reservation.referenceId, 'Reservation expired')
        results.push({reservationId: reservation._id, ...result})
      } catch (error) {
        console.error(`Failed to cleanup reservation ${reservation._id}:`, error)
      }
    }

    return results
  }

  // Get user's active reservations
  async getActiveReservations(userId) {
    return await db
      .collection('transactions')
      .find({
        userId: ObjectId(userId),
        type: 'reserve',
        status: 'pending',
        expiresAt: {$gt: new Date()},
      })
      .toArray()
  }
}

// 4. Integration with Order Processing
class OrderService {
  async createOrder(userId, orderData, giftcardAmount = 0) {
    let reservationId = null

    try {
      // 1. Reserve giftcard balance if needed
      if (giftcardAmount > 0) {
        const reservation = await walletService.reserveBalance(
          userId,
          giftcardAmount,
          orderData.orderId,
          30 // 30 minutes expiration
        )
        reservationId = reservation.reservationId
      }

      // 2. Create order with reservation info
      const order = await db.collection('orders').insertOne({
        ...orderData,
        userId: ObjectId(userId),
        giftcardAmount,
        reservationId,
        status: 'pending_payment',
        createdAt: new Date(),
      })

      return {orderId: order.insertedId, reservationId}
    } catch (error) {
      // If order creation fails, release reservation
      if (reservationId) {
        await walletService.releaseReservation(reservationId, orderData.orderId, 'Order creation failed')
      }
      throw error
    }
  }

  async confirmOrder(orderId) {
    const order = await db.collection('orders').findOne({_id: ObjectId(orderId)})

    if (!order) {
      throw new Error('Order not found')
    }

    try {
      // Confirm giftcard reservation
      if (order.reservationId) {
        await walletService.confirmReservation(order.reservationId, orderId)
      }

      // Update order status
      await db.collection('orders').updateOne(
        {_id: ObjectId(orderId)},
        {
          $set: {
            status: 'confirmed',
            confirmedAt: new Date(),
          },
        }
      )

      return {success: true}
    } catch (error) {
      // Mark order as failed
      await db.collection('orders').updateOne({_id: ObjectId(orderId)}, {$set: {status: 'failed', failedAt: new Date()}})
      throw error
    }
  }

  async cancelOrder(orderId, reason = 'User cancelled') {
    const order = await db.collection('orders').findOne({_id: ObjectId(orderId)})

    if (order && order.reservationId) {
      await walletService.releaseReservation(order.reservationId, orderId, reason)
    }

    await db.collection('orders').updateOne(
      {_id: ObjectId(orderId)},
      {
        $set: {
          status: 'cancelled',
          cancelledAt: new Date(),
          cancellationReason: reason,
        },
      }
    )
  }
}

// 5. Periodic Cleanup Job
class ReservationCleanupJob {
  static async runPeriodically() {
    const walletService = new EnhancedWalletService()
    const results = await walletService.cleanupExpiredReservations()
    console.log(`Cleaned up ${results.length} expired reservations`)
    return results
  }
}

export {EnhancedWalletService, OrderService, ReservationCleanupJob}
```
