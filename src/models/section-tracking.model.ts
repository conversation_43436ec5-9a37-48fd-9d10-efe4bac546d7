// section-tracking-model.ts - A mongoose model
//
// See http://mongoosejs.com/docs/models.html
// for more of what you can do here.
import {Application} from '../declarations'
import {Model, Mongoose} from 'mongoose'

export default function (app: Application): Model<any> {
  const modelName = 'sectionTracking'
  const mongooseClient: Mongoose = app.get('mongooseClient')
  const {Schema} = mongooseClient
  const schema = new Schema(
    {
      sectionId: {type: String, trim: true, required: true}, // section._id
      sectionSnapshot: {type: Object}, // Snapshot of the section at the time of tracking
      status: {type: String, enum: ['completed', 'cancelled'], default: 'completed'}, // Status of the section tracking
      completedTime: {type: Date}, // Time when the section was completed
      cancelledTime: {type: Date}, // Time when the section was cancelled
      cancelledByServicer: {type: Boolean, default: false}, // Flag to indicate if the section was cancelled by the servicer
      creditedPoints: {type: Number, default: 0}, // Points credited to the servicer for the section
      debitedPoints: {type: Number, default: 0}, // Points debited from the booker for the section
      bookingId: {type: String, trim: true, required: true}, // booking._id
      packUser: {type: String, trim: true, required: true}, // service-pack-user._id
      booker: {type: String, trim: true}, // user._id of the person who made the request
      servicer: {type: String, trim: true}, // user._id of the person who accepted the request
      incomeStatus: {type: String, enum: ['actual_pending', 'actual_processed']},
    },
    {
      timestamps: true,
    }
  )

  // This is necessary to avoid model compilation errors in watch mode
  // see https://mongoosejs.com/docs/api/connection.html#connection_Connection-deleteModel
  if (mongooseClient.modelNames().includes(modelName)) {
    ;(mongooseClient as any).deleteModel(modelName)
  }
  return mongooseClient.model<any>(modelName, schema)
}
