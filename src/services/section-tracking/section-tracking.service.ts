// Initializes the `auth` service on path `/auth`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {SectionTracking} from './section-tracking.class'
import createModel from '../../models/section-tracking.model'
import hooks from './section-tracking.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'section-tracking': SectionTracking & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    whitelist: ['$regex', '$options', '$search', '$exists'],
    paginate: app.get('paginate'),
    multi: true,
  }

  // Initialize our service with any options it requires
  app.use('/section-tracking', new SectionTracking(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('section-tracking')

  service.hooks(hooks)
}
