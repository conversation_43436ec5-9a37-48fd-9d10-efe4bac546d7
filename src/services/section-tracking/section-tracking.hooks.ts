import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [],
    get: [hook.toClass],
    create: [
      hook.disallowExternal,
      (d: HookContext) => {
        if (!d.data) {
          return d
        }

        // mark incomeStatus as actual_pending if creditedPoints > 0
        if (Array.isArray(d.data)) {
          d.data.forEach((doc: any) => {
            if (doc.creditedPoints > 0) {
              doc.incomeStatus = 'actual_pending'
            }
          })
        } else if (d.data?.creditedPoints > 0) {
          d.data.incomeStatus = 'actual_pending'
        }
      },
    ],
    update: [hook.disable],
    patch: [hook.disallowExternal],
    remove: [hook.disallowExternal],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
