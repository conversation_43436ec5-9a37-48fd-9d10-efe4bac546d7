import {HookContext} from '@feathersjs/feathers'
import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession} from '../../hooks/dbTransactions'

export class SectionTracking extends Service {
  app: Application
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  async generateServicerIncome(results: any[]) {
    if (!results || results.length === 0) return
    const session = await startTransactionSession(this.app)
    const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}
    try {
      for (const result of results) {
        await this.app.service('income-log').createIncomeLog(
          {
            uid: result.servicer,
            isSchool: false,
            tab: 'earn',
            category: 'associated_task',
            status: 1,
            businessId: result.sectionId,
            event_details: {
              id: result.sectionId,
              name: result.sectionSnapshot?.name,
              cover: result.sectionSnapshot?.taskDetails?.cover,
              sessionStatus: result.status,
            },
            amount: result.creditedPoints * 100,
          },
          transactionParams
        )
        await this.app.service('section-tracking').patch(result._id, {incomeStatus: 'actual_processed'}, transactionParams)
      }
      await commitTransactionSession(session, this.app, transactionParams)
    } catch (error) {
      await rollbackTransactionSession(session)
    }
  }

  async processIncomeCron() {
    console.log('process income cron', new Date())
    // Check if should execute (every 30 minutes = 48 times per day)
    // if (!shouldExecute(48)) {
    //   return
    // }

    try {
      // const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)

      const sectionsToProcess: any = await this.Model.find({
        incomeStatus: 'actual_pending',
      })
        .limit(100)
        .lean()

      if (!sectionsToProcess.length) {
        console.log('Cron: No pending incomes found to process.')
        return
      }

      await this.generateServicerIncome(sectionsToProcess)
    } catch (error) {}
  }
}
