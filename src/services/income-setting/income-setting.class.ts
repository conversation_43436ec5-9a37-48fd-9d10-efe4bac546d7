import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'

export class IncomeSetting extends Service {
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
  }

  async calcIncome({category, amount, tab = 'earn'}: {category: string; amount: number; tab?: string}) {
    const setting: any = await this.Model.findOne({category, tab})

    if (!setting) {
      return {
        success: false,
        income: 0,
      }
    }

    if (setting.mode === 'fixed') {
      return {
        success: true,
        income: setting.value,
      }
    } else {
      let income = 0
      const value = (setting.value / 100) * amount
      if (!value) {
        income = 0
      } else if (value <= 1) {
        income = 1
      } else {
        income = Math.floor(value)
      }
      return {
        success: true,
        income: income,
      }
    }
  }
}
