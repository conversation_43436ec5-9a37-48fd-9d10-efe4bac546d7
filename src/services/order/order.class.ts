import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {Params} from '@feathersjs/feathers'
import {OrderRecovery} from './lib/orderProcessor'
import {OrderCancellation} from './lib/orderCancellation'
import {calculateRefundBreakUp} from './lib/priceUtils'
import {commitTransactionSession, queueForCommit, rollbackTransactionSession, startTransactionSession, TxnParams} from '../../hooks/dbTransactions'
import {IncomeProcessor, isIncomePending} from './lib/incomeProcessor'
const {NotFound, GeneralError, BadRequest} = require('@feathersjs/errors')

export class Order extends Service {
  app: Application
  private orderRecovery: OrderRecovery
  private orderCancellation: OrderCancellation
  private incomeProcessor: IncomeProcessor
  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
    this.orderRecovery = new OrderRecovery(app)
    this.orderCancellation = new OrderCancellation(app)
    this.incomeProcessor = new IncomeProcessor(app)
  }
  async buyCount({id, mode, type}: any, params: Params): Promise<any> {
    const con: any = {status: 200, 'links.id': id, 'links.removed': {$ne: true}}
    if (mode) con['links.mode'] = mode
    if (type) con['links.type'] = type // old
    return await this.app.get('redisHCache')('OrderCount', id, async () => {
      return await this.Model.count(con)
    })
  }
  async buyInfo({id, mode, type}: any, params: Params): Promise<any> {
    const {_id} = params.user ?? {}
    const con: any = {status: 200, buyer: _id, 'links.id': id, 'links.removed': {$ne: true}}
    if (mode) con['links.mode'] = mode
    if (type) con['links.type'] = type // old
    return await this.Model.findOne(con).select('links.id links.mode links.name links.newId links.type links.cover links.hash links.price')
  }
  async extBuyer(one: any, params?: Params) {
    if (one.isSchool) {
      one.buyerInfo = await this.app.service('school-plan').get(one.buyer)
    } else {
      one.buyerInfo = await this.app.service('users').uidToInfo(one.buyer)
    }
    if (one.type == 'prompt') {
      for (let i = 0; i < one.links.length; i++) {
        if (one.links[i].style == 'prompt') {
          one.links[i].goods.userInfo = await this.app.service('users').uidToInfo(one.links[i].goods.uid)
        }
      }
    }
  }
  // 取消未支付订单 | Cancel unpaid order
  async getCancelBeforePay({id, status = 400}: any, params: Params): Promise<any> {
    let order: any = await this.Model.findOne({_id: id})

    if (!order) {
      throw new NotFound('Order not found')
    }
    if (order.status !== 100) {
      throw new BadRequest('Order status error')
    }
    let links = order.links.map((e: any) => {
      e.removed = true
      return e
    })

    const session = await startTransactionSession(this.app)

    try {
      const transactionParams = {...params, mongoose: {session}, sideEffectsToExecute: []}
      if (order.priceBreakdown?.giftCard > 0) {
        await this.app
          .service('gift-card-log')
          .releaseReservedBalance(order.buyer, order.isSchool, order.priceBreakdown.giftCard, order._id.toString(), transactionParams)
      }
      await this.Model.updateOne({_id: id}, {status, links, $unset: {stripeId: '', payMethod: '', paymentRecords: ''}}, {session})

      await commitTransactionSession(session, this.app, transactionParams)
    } catch (error) {
      await rollbackTransactionSession(session)
      throw error
    }

    // Cancel open stripe payment intent as the order is canceled
    this.app.service('stripe').cancelPaymentIntent({paymentIntentId: order.stripeId})
    return {success: true}
  }

  /**
   * @deprecated soon
   * This will be deprecated after next webapp deployment
   * Use getCancelTicketV2 in future
   * 主题服务包 按代金券退款 无积分
   */
  async getCancelTicket({tickets, status = 500}: any, params: Params): Promise<any> {
    let ticketData: any = await this.app.service('service-pack-ticket').Model.find({_id: {$in: tickets}})
    // 验证tickets数据
    let orderId = ''
    for (let i = 0; i < ticketData.length; i++) {
      const e = ticketData[i]
      if (e.refund) {
        return Promise.reject(new GeneralError('Already refunded'))
      }
      if (!e.order) {
        return Promise.reject(new GeneralError('Order not found'))
      }
      if (!orderId) {
        orderId = e.order
      } else {
        if (orderId != e.order) {
          return Promise.reject(new GeneralError('Order not match'))
        }
      }
    }
    let order: any = await this.Model.findOne({_id: orderId}).lean()
    let linkDict: any = {}
    let refundPriceByService: any = {}
    order.links.forEach((e: any) => {
      linkDict[e.id] = e
    })

    // 计算退款金额
    let refundPrice = 0
    for (let i = 0; i < ticketData.length; i++) {
      // const ticket = ticketData[i]
      let {serviceData} = ticketData[i]
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack, cash} = serviceData[j]
        let itemRefundPrice = Number(((cash / linkDict[servicePack].count) * (linkDict[servicePack].price / linkDict[servicePack].persons)).toFixed(0))
        refundPrice += itemRefundPrice
        refundPriceByService[servicePack] = refundPriceByService[servicePack] ? refundPriceByService[servicePack] + itemRefundPrice : itemRefundPrice
      }
    }

    let refundList = []
    // braintree
    if (order.payMethod.indexOf('braintree') > -1 && refundPrice > 0) {
      if (order.settled) {
        let refundResult: any = await this.app.service('braintree').get('refund', {query: {id: order.braintreeId, amount: (refundPrice / 100).toFixed(2)}})
        if (refundResult.success) {
          refundList.push({
            method: 'braintree',
            amount: refundPrice,
            createdAt: new Date(),
            executedAt: new Date(),
            executed: true,
            status: status,
          })
        } else {
          return Promise.reject(new GeneralError(refundResult.message))
        }
      } else {
        refundList.push({
          method: 'braintree',
          amount: refundPrice,
          createdAt: new Date(),
          executed: false,
          status: status,
        })
      }
    }
    // paypal
    if (order.payMethod.indexOf('paypal') > -1 && refundPrice > 0) {
      let refundResult: any = await this.app.service('paypal').get('refund', {query: {id: order.paypalId, amount: (refundPrice / 100).toFixed(2)}})
      if (refundResult.success) {
        refundList.push({
          method: 'paypal',
          amount: refundPrice,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: status,
        })
      } else {
        return Promise.reject(new GeneralError(refundResult.message))
      }
    }
    // 已绑定用户ticket,user-data更新
    for (let i = 0; i < ticketData.length; i++) {
      let {uid, serviceData} = ticketData[i]
      for (let j = 0; j < serviceData.length; j++) {
        let {servicePack, cash, point, gift} = serviceData[j]
        if (uid) {
          let packUserData: any = await this.app.service('service-pack-user').Model.findOne({uid, 'snapshot._id': servicePack})
          let unused = Number((cash + point + gift).toFixed(0))
          await this.app.service('service-pack-user-data').used(
            {
              packUser: packUserData._id,
              type: 'refund',
              times: unused,
              order: orderId,
            },
            params
          )
        }
        if (!uid) {
          await this.app.service('service-pack').Model.updateOne({_id: servicePack}, {$inc: {'count.ticket': -1}})
        }
      }
    }

    // 更新link.refundPrice
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (refundPriceByService[link.id]) {
        link.refundPrice = (link.refundPrice + refundPriceByService[link.id]).toFixed(0)
      }
    }

    await this.app.service('order').patch(orderId, {$push: {refund: {$each: refundList}}, links: order.links})
    await this.app.service('service-pack-ticket').Model.updateMany({_id: {$in: tickets}}, {refund: true, $unset: {uid: ''}})
    await this.app.service('service-pack-apply').Model.updateMany({serviceTicket: {$in: tickets}}, {$pull: {serviceTicket: {$in: tickets}}})

    // ticket全部退完以后 把订单剩余部分退款
    let notRefundTicket = await this.app.service('service-pack-ticket').Model.find({order: orderId, refund: false})
    if (notRefundTicket.length == 0) {
      let links = order.links.map((e: any) => {
        if (e.style == 'service') {
          e.removed = true
        }
        return e
      })
      await this.app.service('order').patch(orderId, {links})
      this.getCancel({id: orderId, status: status}, params)
    }
    return {ok: 1}
  }

  // to be called from frontend, to call internally use cancelTicketController
  async getCancelTicketV2(data: any, params: Params): Promise<any> {
    const session = await startTransactionSession(this.app)
    try {
      const transactionParams = {...params, mongoose: {session}, sideEffectsToExecute: []}
      const result = await this.orderCancellation.cancelTicketController(data, transactionParams)
      await commitTransactionSession(session, this.app, transactionParams)
      return result
    } catch (error) {
      await rollbackTransactionSession(session)
      throw error
    }
  }

  // 按link.id批量取消订单 用于公开课未成团/下架 取消订单
  async getCancelByLinkId({linkId, status, childSessions}: any, params: TxnParams): Promise<any> {
    const orders = await this.Model.find({status: 200, 'links.id': linkId})
    console.log('getCancelByLinkId', orders.length)
    await Promise.all(orders.map((order) => this.orderCancellation.cancelOrderController({id: order._id, status, linkIds: [linkId], childSessions}, params)))

    return {success: true}
  }

  /**
   * @deprecated soon
   * This will be deprecated after next webapp deployment.
   * Use getCancelV2 in future
   */
  async getCancel({id, status, linkIds}: any, params: Params): Promise<any> {
    let {refundAllowed, message, order, refundPrice, refundPoint, refundLinkName, refundLinkCover, refundLinkIds} = await this.getOrderRefundCheck({
      id,
      status,
      linkIds,
    })
    if (!refundAllowed) {
      return Promise.reject(new GeneralError(message))
    }
    if (order.type == 'service_premium' && order.isSchool && order.isTicket) {
      let tickets = await this.app.service('service-pack-ticket').Model.find({order: order._id, refund: false}).lean()
      if (tickets.length > 0) {
        return this.getCancelTicket({tickets: tickets.map((e) => e._id), status}, params)
      }
      // let serviceIds = order.links.filter((item: any) => !item.removed && item.style == 'service')
    }

    let user = await this.app.service('users').uidToInfo(order.buyer)
    if (order.isSchool) {
      user = await this.app.service('users').uidToInfo(order.schoolAdmin)
    }

    let refundList = []
    let isAllRefund = order.links.every((e: any) => e.removed)
    if (order.paid === 0) {
      let patchData: any = {links: order.links}
      if (isAllRefund) {
        patchData.status = status
      }
      this.app.service('order').patch(id, patchData)
      await this.orderCancellation.handleLinkRefund(order, refundPrice, params)
      return {success: true, id, status}
    } else if (order.paid == 1) {
      // TODO gift card
      if (!order.isPoint) {
        // 现金退款 braintree
        if (order.payMethod.indexOf('braintree') > -1 && refundPrice > 0) {
          if (order.settled) {
            let refundResult: any = await this.app.service('braintree').get('refund', {query: {id: order.braintreeId, amount: (refundPrice / 100).toFixed(2)}})
            if (refundResult.success) {
              refundList.push({
                method: 'braintree',
                amount: refundPrice,
                createdAt: new Date(),
                executedAt: new Date(),
                executed: true,
                status: status,
              })
            } else {
              return Promise.reject(new GeneralError(refundResult.message))
            }
          } else {
            refundList.push({
              method: 'braintree',
              amount: refundPrice,
              createdAt: new Date(),
              executed: false,
              status: status,
            })
          }
        }
        // 现金退款 paypal
        if (order.payMethod.indexOf('paypal') > -1 && refundPrice > 0) {
          let refundResult: any = await this.app.service('paypal').get('refund', {query: {id: order.paypalId, amount: (refundPrice / 100).toFixed(2)}})
          if (refundResult.success) {
            refundList.push({
              method: 'paypal',
              amount: refundPrice,
              createdAt: new Date(),
              executedAt: new Date(),
              executed: true,
              status: status,
            })
          } else {
            return Promise.reject(new GeneralError(refundResult.message))
          }
        }
      } else {
        // 积分退款
        await this.app.service('point-log').getAddLog({
          tab: 'earn',
          uid: order.buyer,
          source: 'refund',
          category: 'refund',
          change: refundPoint,
          businessId: order._id,
          snapshot: order,
        })
        refundList.push({
          method: 'point',
          amount: refundPoint,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: status,
        })
      }
      let patchData: any = {links: order.links, $push: {refund: {$each: refundList}}}
      if (isAllRefund) {
        patchData.status = status
        patchData.paid = 2
      }
      await this.app.service('order').patch(id, patchData)
      await this.orderCancellation.handleLinkRefund(order, refundPrice, params)

      // email refund success
      if (order.settled) {
        let url = `${SiteUrl}/v2/order/payHistory/${order._id}`
        let url2 = `${SiteUrl}/v2/order/detail/${order._id}`

        this.app.service('notice-tpl').send(
          'OrderRefundSuccess',
          {_id: user._id, email: user.email},
          {
            username: user.name.join(' '),
            gift_card_amount: '0.00',
            cash_amount: (refundPrice / 100).toFixed(2),
            no: order.no,
            amount: (order.price / 100).toFixed(2),
            date: new Date().toLocaleString(),
            url: url,
            link_name: refundLinkName.join(', '),
            url2: url2,
            image: hashToUrl(refundLinkCover[0] || ''),
            addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
          }
        )
      }
      return {success: true, id, status}
    }
  }

  /**
   * 订单取消并退款 整单处理
   * 学校购买的ticket,单独处理退款
   * linkId 不传或为空则全部退款
   * 除400外的4xx弃用
   * 401.未支付 公开课被讲师取消 canceled by the facilitator
   * 402.未支付 公开课因未成团被系统取消 Minimal registration number not met
   * 403.未支付 课件/自学习被下架 Product removed
   * 500.已支付 公开课/服务包被购买者取消 canceled by the purchaser
   * 501.已支付 公开课被讲师取消 canceled by the facilitator
   * 502.已支付 公开课因未成团被系统取消 Minimal registration number not met
   * 503.已支付 支付前被下架/删除,支付后立即退款
   */
  async getCancelV2(data: any, params: TxnParams): Promise<any> {
    const session = await startTransactionSession(this.app)
    try {
      const transactionParams = {...params, mongoose: {session}, sideEffectsToExecute: []}
      const result = await this.orderCancellation.cancelOrderController(data, transactionParams)
      await commitTransactionSession(session, this.app, transactionParams)
      return result
    } catch (error) {
      await rollbackTransactionSession(session)
      throw error
    }
  }

  // 检查订单能否退款 并生成退款信息
  // 500为手动取消,需判断退款条件,501-503为系统取消,无需判断退款条件
  async getOrderRefundCheck(
    {id, status = 500, linkIds, childSessions}: {id: string; status: number; linkIds?: string[]; childSessions?: any[]},
    params?: TxnParams
  ): Promise<any> {
    const options = Acan.getTxnOptions(params)
    const order: any = await this.Model.findOne({_id: id}, null, options).lean()
    const {type} = order
    let refundPrice = 0
    let refundPoint = 0
    let refundLinkName = []
    let refundLinkCover = []
    let separateAllowed = true // 可结算 针对代课和task
    let isRemoved = true

    if (order.status !== 200) {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'Order status error',
      }
    }
    if (type == 'session_self_study') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for self-study contents enrolled from Classcipe Center.',
      }
    }
    if (type == 'unit') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for teaching resources purchased from Library.',
      }
    }
    if (type == 'premium_cloud') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for import cloud.',
      }
    }
    if (type == 'prompt') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for prompt.',
      }
    }

    if (type === 'gift_card') {
      return {
        refundAllowed: false,
        separateAllowed: true,
        message: 'No cancellation for gift card.',
      }
    }

    // 需退款的link
    let refundLinkIds = []
    if (!linkIds || linkIds.length == 0) {
      refundLinkIds = order.links.map((item: any) => item.id)
    } else {
      refundLinkIds = linkIds
    }
    for (let i = 0; i < order.links.length; i++) {
      let link = order.links[i]
      if (link.removed) {
        continue
      }
      if (!refundLinkIds.includes(link.id)) {
        continue
      }
      if (link.style === 'session') {
        const result = await this.orderCancellation.calcSessionRefund({order, link, status, childSessions}, params)

        if (!result.valid) {
          return result.response
        }

        if (order.isPoint) {
          refundPoint += result.refundAmount
          link.refundPoint = (link.refundPoint || 0) + result.refundAmount
        } else {
          refundPrice += result.refundAmount
          link.refundPrice = (link.refundPrice || 0) + result.refundAmount
          link.currentRefundAmount = result.refundAmount
        }
        link.refundedItems = result.refundedItems || link.refundedItems
        isRemoved = result.isRemaining ? false : true
        separateAllowed = false
      } else if (['section_top_up', 'remaining_sections'].includes(link.style) || (link.style === 'service' && link.type === 'serviceTask')) {
        if (!order.isSchool) {
          const result = await this.orderCancellation.calcServiceTaskRefund(
            {
              order,
              discountConfig: link.discountConfig,
            },
            params
          )
          if (!result.valid) {
            return {
              refundAllowed: false,
              cancelDisabled: 'serviceTask',
              separateAllowed: true,
              message: result.message,
            }
          }
          refundPrice += result.refundPrice
          link.refundPrice = result.refundPrice
        }
      } else if (link.style === 'service' && link.type !== 'serviceTask') {
        // 无主题
        if (new Date(order.createdAt).getTime() + 14 * 24 * 60 * 60 * 1000 < Date.now()) {
          return {
            refundAllowed: false,
            cancelDisabled: true,
            separateAllowed: true,
            message: 'Free cancellation of the remaining within 14 days.',
          }
        } else {
          separateAllowed = false
        }
        if (!order.isSchool) {
          let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, 'snapshot._id': link.id}, null, options)
          let serviceCount = await this.calcServiceCount({packUser, orderId: order._id, orderDoc: order, linkItem: link}, params)
          let remainingCount = order.settled ? serviceCount.refundCount : 0

          if (!order.isPoint) {
            // refundPrice += link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
            // link.refundPrice = link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
            let calcRefundPrice = this.calcPriceServiceRefund({link, remainingCount, order})
            refundPrice += calcRefundPrice
            link.refundPrice = calcRefundPrice
          } else {
            refundPoint += link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
            link.refundPoint = link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
          }
        }
      } else if (link.style === 'service_substitute') {
        // 代课 永久可退 30天后扣除总金额30%
        let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, 'snapshot._id': link.id, pid: {$exists: false}})
        if (link.isOnCampus) {
          packUser = await this.app.service('service-pack-user').Model.findOne({pid: packUser._id, country: link.country, city: link.city})
        }
        // else {
        //   if (new Date(order.createdAt).getTime() + 14 * 24 * 60 * 60 * 1000 < Date.now()) {
        //     return {
        //       refundAllowed: false,
        //       message: 'Free cancellation of the remaining within 14 days.',
        //     }
        //   }
        // }
        let isOver30Days = false
        if (new Date(order.createdAt).getTime() + 30 * 24 * 60 * 60 * 1000 < Date.now()) {
          isOver30Days = true
        } else {
          separateAllowed = false
        }
        if (!packUser) {
          continue
        }
        let serviceCount = await this.calcServiceCountSubstitute(packUser, link, order.isPoint)
        let remainingCount = order.settled ? serviceCount.refundCount : 0
        if (!order.isPoint) {
          let itemRefundPrice = link.count ? Number(((remainingCount / (link.count * 60)) * link.price * (isOver30Days ? 0.7 : 1)).toFixed(0)) : 0
          refundPrice += itemRefundPrice
          link.refundPrice = itemRefundPrice
        } else {
          let itemRefundPoint = link.count ? Number(((remainingCount / (link.count * 60)) * link.point * (isOver30Days ? 0.7 : 1)).toFixed(0)) : 0
          refundPoint += itemRefundPoint
          link.refundPoint = itemRefundPoint
        }
      } else if (link.style === 'service_premium') {
        if (new Date(order.createdAt).getTime() + 14 * 24 * 60 * 60 * 1000 < Date.now()) {
          return {
            refundAllowed: false,
            cancelDisabled: true,
            separateAllowed: true,
            message: 'Free cancellation of the remaining within 14 days.',
          }
        } else {
          separateAllowed = false
        }

        let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: order.buyer, order: order._id, premium: link.id})
        if (!packUser) {
          continue
        }
        let serviceCount = await this.calcServiceCount({packUser, orderId: order._id, orderDoc: order, linkItem: link}, params)
        let remainingCount = order.settled ? serviceCount.refundCount : 0
        if (!order.isPoint) {
          refundPrice += link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
          link.refundPrice = link.count ? Number(((remainingCount / link.count) * link.price).toFixed(0)) : 0
        } else {
          refundPoint += link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
          link.refundPoint = link.count ? Number(((remainingCount / link.count) * link.point).toFixed(0)) : 0
        }
      } else {
        if (!order.isPoint) {
          refundPrice += link.price
          link.refundPrice = link.price
        } else {
          refundPoint += link.point
          link.refundPoint = link.point
        }
      }
      refundLinkName.push(link.name)
      refundLinkCover.push(link.cover)
      link.removed = isRemoved
      link.pending = true

      if (!params?.provider && link.refundPrice > link.price) {
        throw new GeneralError('Invalid refund amount')
      }
    }
    return {
      refundAllowed: refundLinkName.length > 0 ? true : false,
      separateAllowed,
      message: refundLinkName.length > 0 ? '' : 'All items in the order already cancelled.',
      order,
      refundPrice,
      refundPoint,
      refundLinkName,
      refundLinkCover,
      refundLinkIds,
    }
  }

  // 计算服务包退款价格 已使用按原始价格扣除(不计算批发价,只计算折扣) #5814
  calcPriceServiceRefund({link, remainingCount, order}: any) {
    if (remainingCount <= 0) {
      return 0
    }
    let {price, goods, count, discountConfig} = link
    let {price: baseUnitPrice, discount} = goods
    discountConfig = discountConfig ?? goods.discountConfig // to handle old orders with deprecated goods.discountConfig
    let isDiscountConfig =
      discountConfig && discountConfig.enable && (!discountConfig.end || new Date(order.createdAt).getTime() < new Date(discountConfig.end).getTime())

    // Calculate effective unit price based on USED count, not original purchase count
    let usedCount = count - remainingCount
    let effectiveUnitPriceForUsedItems = baseUnitPrice

    // Check if batch discounts exist in link.discount array
    if (discount && Array.isArray(discount) && discount.length > 0 && discount.some((e: any) => e.discount > 0)) {
      let current: any = false
      let discountSort = discount.sort((a: any, b: any) => a.count - b.count)

      // Find the applicable batch discount based on USED count (not original purchase count)
      for (let i = 0; i < discountSort.length; i++) {
        const item = discountSort[i]
        if (usedCount >= item.count) {
          current = item
        } else if (usedCount < item.count) {
          break
        }
      }

      // Apply batch discount to get effective unit price for used items
      if (current) {
        effectiveUnitPriceForUsedItems = (baseUnitPrice * (100 - current.discount)) / 100
      }
    }

    // Calculate refund: total paid - (effective price for used items × used count)
    let refundPrice = 0
    refundPrice = count
      ? Number((price - effectiveUnitPriceForUsedItems * usedCount * (isDiscountConfig ? (100 - discountConfig.discount) / 100 : 1)).toFixed(0))
      : 0
    return refundPrice > 0 ? refundPrice : 0
  }
  // 检查商品列表是否可购买
  async getCheckLinks({links = [], servicePremium, sharedSchool}: any, params: Params): Promise<any> {
    const {_id} = params.user ?? {}
    let ordered: String[] = []
    const hasGiftCard = links.some((item: any) => item.style === 'gift_card')
    if (hasGiftCard && links.length > 1) {
      return Promise.reject(new BadRequest('Gift card can only be purchased alone'))
    }
    let unpaidOrder: any[] = []
    let order: any = []
    if (servicePremium) {
      unpaidOrder = await this.Model.find({
        buyer: _id,
        status: 100,
        servicePremium,
        sharedSchool,
      })
      order = await this.Model.find({
        buyer: _id,
        status: {$in: [200]},
        servicePremium,
        sharedSchool,
      })
    } else if (!hasGiftCard) {
      // check paid order
      let existLinkIds = links.filter((item: any) => item.style === 'unit' || item.style === 'session').map((item: any) => item.id)
      order = await this.Model.find({
        buyer: _id,
        status: {$in: [200]},
        links: {$elemMatch: {id: {$in: existLinkIds}, removed: {$exists: false}}},
        type: {$ne: 'service_premium'},
      })
      order.forEach((item: any) => {
        item.links.forEach((link: any) => {
          if (existLinkIds.indexOf(link.id) > -1 && !link.removed) {
            ordered.push(link.id)
          }
        })
      })
      // check unpaid order
      let unpaidLinkIds = links.map((item: any) => item.id)
      unpaidOrder = await this.Model.find({
        buyer: _id,
        status: 100,
        links: {$elemMatch: {id: {$in: unpaidLinkIds}, removed: {$exists: false}}},
        type: {$ne: 'service_premium'},
      })
      unpaidOrder.forEach((item: any) => {
        item.links.forEach((link: any) => {
          if (existLinkIds.indexOf(link.id) > -1 && !link.removed) {
            ordered.push(link.id)
          }
        })
      })
    }

    for (let i = 0; i < links.length; i++) {
      const item = links[i]
      if (ordered.indexOf(item.id) > -1) {
        item.ordered = true
      }
      if (item.style === 'unit') {
        let unit
        try {
          unit = await this.app.service('unit').get(item.id)
        } catch (error) {
          unit = false
        }
        if (!unit || !unit.publish.lib) {
          item.removed = true
        }
      } else if (item.style === 'session') {
        let session
        try {
          session = await this.app.service('session').get(item.id)
        } catch (error) {
          session = false
        }
        if (!session) {
          item.removed = true
        }
        if (session.type !== 'selfStudy' && Date.now() > new Date(session.regDate).getTime()) {
          item.removed = true
        }
      } else if (item.style === 'service' || item.style === 'service_substitute') {
        let service
        try {
          service = await this.app.service('service-pack').get(item.id)
        } catch (error) {
          service = false
        }
        if (!service || !service.status) {
          item.removed = true
        }
      } else if (item.style === 'service_premium') {
        let auth
        try {
          auth = await this.app.service('service-auth').Model.findOne({_id: item.id})
        } catch (error) {
          auth = false
        }

        if (!auth) {
          item.removed = true
        }
      } else if (item.style === 'section_top_up') {
        const deficitCredit: any = await this.app.service('section').getDeficitCredit({sectionId: item.id}, params)
        if (deficitCredit === 0) {
          item.removed = true
        }
      } else if (item.style === 'remaining_sections') {
        try {
          await this.app.service('section').getRemainingSections({packUserId: item.id}, params)
        } catch (error) {
          item.removed = true
        }
      }
    }
    let available = links.filter((item: any) => !item.removed && ordered.indexOf(item.id) == -1).map((item: any) => item.id)
    let notExist = links.filter((item: any) => item.removed).map((item: any) => item.id)
    let paidOrderId = order.map((item: any) => item._id)
    let unpaidOrderId = unpaidOrder.map((item: any) => item._id)
    let orderId = [...paidOrderId, ...unpaidOrderId]
    let servicePremiumAvailable = servicePremium && !notExist.length && !unpaidOrder.length && !order.length
    return {links, available, notExist, ordered, orderId, paidOrderId, unpaidOrderId, servicePremiumAvailable}
  }
  // paypal支付完成后 前端主动回调
  async getCheckPaypalPayment({id, paypalOrderId}: any, params: Params): Promise<any> {
    let paypalDetail: any
    try {
      paypalDetail = await this.app.service('paypal').paypalOrderRequest(paypalOrderId)
    } catch (error) {
      return Promise.reject(new GeneralError(error))
    }
    if (paypalDetail.status === 'COMPLETED') {
      await this.processOrderCompletion(
        id,
        {
          paypalId: paypalDetail.purchase_units[0].payments.captures[0].id,
          settled: true,
          $push: {
            payMethod: 'paypal',
            paymentRecords: {
              method: 'paypal',
              status: 'paid',
              amount: paypalDetail.purchase_units[0].payments.captures[0].amount.value * 100,
              transactionId: paypalDetail.purchase_units[0].payments.captures[0].id,
              paidAt: new Date(),
            },
          },
          paidAt: new Date(),
          // paymentInfo: {
          //   paymentInstrumentType: 'paypal_account',
          //   cardType: '',
          //   last4: '',
          // },
        },
        'api'
      )
      return true
    } else {
      return false
    }
  }

  // async excGoods(order: any, params?: Params) {
  //   if (order.status !== 400) {
  //     return order
  //   }
  //   for (let i = 0; i < order.links.length; i++) {
  //     let link = order.links[i]
  //     let goods
  //     try {
  //       if (link.style === 'session') {
  //         goods = await this.app.service('session').get(link.id)
  //       } else if (link.style === 'unit') {
  //         goods = await this.app.service('unit').get(link.id)
  //       }
  //     } catch (e) {
  //       goods = {}
  //     }
  //     link.goods = goods
  //   }
  //   return order
  // }
  async expirationDate(order: any, params?: Params) {
    if (order.status !== 100) {
      return order
    }
    let expirationArr = []
    for (let i = 0; i < order.links.length; i++) {
      const link = order.links[i]
      if (order.type === 'session_public' && link.style === 'session') {
        let session = (await this.app.service('session').Model.findById(link.id).select(['regDate'])) as any
        if (session.regDate) {
          expirationArr.push(new Date(session.regDate).getTime())
        }
      }
    }
    expirationArr.push(new Date(order.createdAt).getTime() + 12 * 60 * 60 * 1000)
    return expirationArr.sort().shift()
  }
  // 支付后检查商品,计算退款信息但不执行退款 积分购买无需检查(下单扣费一步完成)
  async getRefundInvalidLinks({orderId}: any, params: any) {
    let order: any = await this.app.service('order').Model.findOne({_id: orderId}).lean()
    let myLinks = Acan.clone(order.links)
    let sessionRemoved = false
    let refundLinkName: any = []
    let refundLinkCover: any = []
    let servicePremiumData: any

    if (order.servicePremium) {
      servicePremiumData = await this.app.service('service-pack').Model.findOne({_id: order.servicePremium}).lean()
    }

    // Process all links in parallel
    const linkPromises = myLinks.map(async (item: any) => {
      if (Acan.isObjectId(item.id)) {
        if (item.style === 'unit') {
          let unit: any = await this.app.service('unit').Model.findOne({_id: item.id}).lean()
          if (!unit || !unit.publish.lib) {
            item.removed = true
            item.refundPrice = item.price
            return {
              refundLinkName: item.name,
              refundLinkCover: item.cover,
              sessionRemoved: false,
            }
          }
        } else if (item.style === 'session') {
          let session: any = await this.app.service('session').Model.findOne({_id: item.id}).lean()
          if (!session) {
            item.removed = true
            item.refundPrice = item.price
            return {
              refundLinkName: item.name,
              refundLinkCover: item.cover,
              sessionRemoved: true,
            }
          }
        } else if (item.style === 'service' || item.style === 'service_substitute') {
          let service: any = await this.app.service('service-pack').Model.findOne({_id: item.id}).lean()
          if (!service || !service.status || (order.servicePremium && !servicePremiumData?.status)) {
            item.removed = true
            item.refundPrice = item.price
            return {
              refundLinkName: item.name,
              refundLinkCover: item.cover,
              sessionRemoved: false,
            }
          }
        } else if (item.style === 'service_premium') {
          let lecture: any = await this.app.service('service-auth').Model.findOne({_id: item.id}).lean()
          if (!lecture || lecture.status != 2 || (order.servicePremium && !servicePremiumData?.status)) {
            item.removed = true
            item.refundPrice = item.price
            return {
              refundLinkName: item.name,
              refundLinkCover: item.cover,
              sessionRemoved: false,
            }
          } else if (item.style === 'section_top_up') {
            const deficitCredit: any = await this.app.service('section').getDeficitCredit({sectionId: item.id}, params)
            if (deficitCredit !== item.price) {
              item.removed = true
              item.refundPrice = item.price
              return {
                refundLinkName: item.name,
                refundLinkCover: item.cover,
                sessionRemoved: false,
              }
            }
          } else if (item.style === 'remaining_sections') {
            try {
              await this.app.service('section').getRemainingSections({packUserId: item.id}, params)
            } catch (error) {
              item.removed = true
              item.refundPrice = item.price
              return {
                refundLinkName: item.name,
                refundLinkCover: item.cover,
                sessionRemoved: false,
              }
            }
          }
        }
      }
      return null
    })

    // Wait for all link processing to complete
    const linkResults = await Promise.all(linkPromises)

    // Process results and build refund arrays
    linkResults.forEach((result) => {
      if (result) {
        refundLinkName.push(result.refundLinkName)
        refundLinkCover.push(result.refundLinkCover)
        if (result.sessionRemoved) {
          sessionRemoved = true
        }
      }
    })

    if (order.type == 'session_service_pack' && sessionRemoved) {
      myLinks = myLinks.map((item: any) => {
        if (item.style === 'service') {
          item.removed = true
          item.refundPrice = item.price
          refundLinkName.push(item.name)
        }
        return item
      })
    }

    let invalidLinks = myLinks.filter((item: any) => item.removed)
    let refundPrice = invalidLinks.reduce((prev: any, cur: any) => {
      return prev + cur.price
    }, 0)

    // Return refund calculation results without performing actual refund
    return {
      refundPrice,
      invalidLinks,
      refundLinkName,
      refundLinkCover,
    }
  }
  // 订单数量统计
  async getCount({}: any, params: Params): Promise<any> {
    let unpaid = await this.Model.count({buyer: params.user?._id, status: 100})
    let paid = await this.Model.count({buyer: params.user?._id, status: 200})
    return {
      unpaid,
      paid,
    }
  }

  // 价格计算 unit/session
  async calcPrice(discount: any, isPoint = false, style: string, goods: any, createdAt: Date) {
    if (!discount) {
      return 0
    }
    let price = 0
    if (style == 'self_study') {
      let incomeSetting: any = await this.app.service('income-setting').Model.findOne({tab: 'claim', category: 'self_study'})
      if (incomeSetting) {
        price = Number((incomeSetting.value * goods.questions.length).toFixed(0))
      } else {
        price = 0
      }
    } else {
      if (discount.val) {
        if (discount.end && new Date(discount.end).getTime() < new Date(createdAt).getTime()) {
          price += Number(discount.price || 0)
        } else {
          price += ((discount.price || 0) * (100 - discount.val)) / 100
        }
      } else {
        price += Number(discount.price || 0)
      }
    }
    if (!isPoint) {
      return {
        success: true,
        price: Number(price.toFixed(0)),
        point: 0,
      }
    } else {
      return await this.app.service('point-setting').calcPoint({type: {category: style}, amount: price, isPoint: false})
    }
  }
  // Check if service task payload is valid
  isValidServiceTask({item, sections}: any) {
    if (!Array.isArray(sections) || sections.length === 0) {
      return false
    }

    if (item.sectionCount && item.sectionCount > 1) {
      return false
    }

    return true
  }
  // Calculate unit price for service task
  serviceTaskUnitPrice({goods, sectionCount, unitPrice, discountConfig}: {goods: any; sectionCount: number; unitPrice: number; discountConfig?: any}) {
    if (goods.sections.length > 1 && sectionCount === 1) {
      return {
        unitPrice: goods.sections[0].salesPrice * 100,
        discountConfig: {enable: false},
      }
    }

    return {
      unitPrice,
      discountConfig,
    }
  }
  // 服务包价格计算
  async calcPriceService({discount, unitPrice, count, discountConfig, isPoint, persons = 1, style, createdAt}: any) {
    let price = 0
    let current: any = false
    let giftCount = 0
    let discountSort = discount.sort((a: any, b: any) => a.count - b.count)
    let isDiscountConfig =
      discountConfig &&
      discountConfig.enable &&
      discountConfig.discount &&
      (!discountConfig.end || new Date(createdAt).getTime() < new Date(discountConfig.end).getTime())
    for (let i = 0; i < discountSort.length; i++) {
      const item = discountSort[i]
      if (count >= item.count) {
        current = item
      } else if (count < item.count) {
        break
      }
    }

    // 批发
    if (current) {
      giftCount = current.gifts
    }
    if (current) {
      price += (unitPrice * count * (100 - current.discount)) / 100
    } else {
      price += Number(unitPrice * count)
    }
    // 折扣
    if (isDiscountConfig) {
      price = (price * (100 - discountConfig.discount)) / 100
    }
    price = price * persons

    if (!isPoint) {
      return {
        success: true,
        price: Number(price.toFixed(0)),
        point: 0,
        giftCount,
      }
    } else {
      let res = await this.app.service('point-setting').calcPoint({type: {category: style}, amount: price, isPoint: false})
      return {
        ...res,
        giftCount,
      }
    }
  }
  // 主题服务包价格计算
  async calcPriceServicePremium({
    premium,
    goods,
    sharedSchool,
    isPoint,
    isSchool,
    count,
    noDiscount,
    buyer,
    createdAt,
  }: {
    premium: string
    goods: any
    sharedSchool: any
    isPoint: boolean
    isSchool: boolean
    count: any
    noDiscount: boolean
    buyer: string
    createdAt: Date
  }): Promise<any> {
    let contentOrientated
    let current: any
    let price = 0
    let discountConfig = goods.discountConfig
    if (sharedSchool) {
      let schoolPrice: any = await this.app.service('service-pack-apply').Model.findOne({uid: buyer, sharedSchool, servicePack: goods._id})
      contentOrientated = schoolPrice.contentOrientated
    } else {
      contentOrientated = goods.contentOrientated
    }
    contentOrientated.forEach((e: any) => {
      if (e.premium == premium) {
        current = e
      }
    })
    price = Number(((isSchool ? current.schoolPrice : current.price) * (count || current.times)).toFixed(0))
    if (
      !sharedSchool &&
      discountConfig.enable &&
      (!discountConfig.end || new Date(createdAt).getTime() < new Date(discountConfig.end).getTime()) &&
      !noDiscount
    ) {
      price = Number(((price * (100 - discountConfig.discount)) / 100).toFixed(0))
    }
    if (!isPoint) {
      return {
        success: true,
        price,
        point: 0,
        count: count || current.times,
      }
    } else {
      let res = await this.app.service('point-setting').calcPoint({type: {category: 'service_premium'}, amount: price, isPoint: false})
      return {
        ...res,
        count: count || current.times,
      }
    }
  }
  // 订单settled后 待处理link执行退款
  async dispatchRefund(order: any, params: any) {
    let refundList = order.refund
    let refundLinkName = order.links.filter((e: any) => e.removed).map((e: any) => e.name)
    let refundLinkCover = order.links.filter((e: any) => e.removed).map((e: any) => e.cover)
    const user = await this.app.service('users').uidToInfo(order.buyer)
    for (let i = 0; i < refundList.length; i++) {
      const item = refundList[i]
      if (item.method === 'braintree') {
        let refundResult: any = await this.app
          .service('braintree')
          .get('refund', {query: {id: order.braintreeId, amount: (parseFloat(item.amount) / 100).toFixed(2)}})
        if (refundResult.success) {
          refundList[i].executed = true
          refundList[i].executedAt = new Date()
          // email refund success
          let url = `${SiteUrl}/v2/order/payHistory/${order._id}`
          let url2 = `${SiteUrl}/v2/order/detail/${order._id}`
          this.app.service('notice-tpl').send(
            'OrderRefundInProcess',
            {_id: user._id, email: user.email},
            {
              username: user.name.join(' '),
              url: url,
              link_name: refundLinkName.join('<br>'),
            }
          )
          this.app.service('notice-tpl').send(
            'OrderRefundSuccess',
            {_id: user._id, email: user.email},
            {
              username: user.name.join(' '),
              gift_card_amount: '0.00',
              cash_amount: (parseFloat(item.amount) / 100).toFixed(2),
              no: order.no,
              amount: (parseFloat(order.price) / 100).toFixed(2),
              date: new Date(),
              url: url,
              link_name: refundLinkName.join(', '),
              url2: url2,
              image: hashToUrl(refundLinkCover[0] || ''),
              addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
            }
          )
        }
      }
    }
    this.app.service('order').patch(order._id, {$set: {refund: refundList}})
  }

  // 判断主题服务包的类型 购买了哪种服务包 'lecture', 'mentor', 'all'
  getServicePremiumType(links: any[]): string {
    let hasService = false
    let hasPremium = false
    for (let i = 0; i < links.length; i++) {
      const link: any = links[i]
      if (link.style === 'service' || link.style === 'service_substitute') {
        hasService = true
      }
      if (link.style === 'service_premium') {
        hasPremium = true
      }
    }
    if (hasService && hasPremium) {
      return 'all'
    } else if (hasService) {
      return 'mentor'
    } else if (hasPremium) {
      return 'lecture'
    } else {
      return 'no type'
    }
  }

  // Expose as public method
  async processOrderCompletion(orderId: string, paymentDetails: any, source: 'api' | 'webhook' = 'webhook') {
    return this.orderRecovery.processOrderCompletion(orderId, paymentDetails, source)
  }

  async processOrderLinks(...a: [any, any, any]) {
    return this.orderRecovery.processOrderLinks(...a)
  }

  async processRefund(orderId: string, refundInfo: any, orderDoc?: any, isRetry?: boolean) {
    return this.orderRecovery.processRefund(orderId, refundInfo, orderDoc, isRetry)
  }

  async sendRefundSuccessNotification(data: any) {
    return this.orderRecovery.sendRefundSuccessNotification(data)
  }

  // 支付后更新订单status,paid
  async completeOrder(order: any, params?: TxnParams) {
    // 获取事务参数
    const options = Acan.getTxnOptions(params)
    // 处理事务参数
    const transactionParams = Acan.mergeTxnParams(params)
    // 解构订单信息
    let {_id, links, type, isPoint, isSchool, isTicket, persons, buyer, servicePremium, sharedSchool, servicePackApply} = order
    // 购买用户 个人/学校
    let user
    // 通知对象
    let noticeUser
    // 处理user参数 个人/学校
    if (!isSchool) {
      // 获取用户数据
      user = Acan.clone(await this.app.service('users').uidToInfo(order.buyer))
      // 转换uid为字符串
      user._id = user._id.toString()
      // 设置通知对象为买家 个人购买
      noticeUser = user
    } else {
      // 如果是学校，通知对象为学校管理员 学校购买
      noticeUser = Acan.clone(await this.app.service('users').uidToInfo(order.schoolAdmin))
      // 转换uid为字符串
      noticeUser._id = noticeUser._id.toString()
      // 处理user参数
      user = {_id: buyer}
    }

    // 获取退款信息，检查无效商品
    let refundInfo = await this.app.service('order').getRefundInvalidLinks({orderId: order._id}, {})
    // 解构退款信息
    let {refundPrice, invalidLinks, refundLinkName, refundLinkCover} = refundInfo

    // 失效商品ID
    let invalidLinksId = invalidLinks.map((item: any) => item.id)

    const {links: updatedLinks} = await this.processOrderLinks(
      {
        links,
        user,
        isOrderCreation: false,
        invalidLinks: invalidLinks,
        invalidLinksId: invalidLinksId,
      },
      order,
      params
    )

    links = updatedLinks

    let status = 200
    // 设置支付状态为已支付
    let paid = 1

    // 如果所有商品都无效
    if (invalidLinks.length == links.length) {
      // 设置状态为503
      status = 503
      // 设置支付状态为已退款
      paid = 2
    } else {
      // 发送支付成功邮件
      let url = `${SiteUrl}/v2/order/receipt/${order._id}`
      let url2 = `${SiteUrl}/v2/order/detail/${order._id}`
      // 将邮件发送任务加入队列
      queueForCommit(
        this.app,
        'notice-tpl',
        'send',
        [
          'OrderPaySuccess',
          {_id: noticeUser._id, email: noticeUser.email},
          {
            name: `${links[0].name}${links.length > 1 ? ' +' + (links.length - 1) + ' products' : ''}`,
            url: url,
            url2: url2,
            image: hashToUrl(links[0].cover || ''),
          },
        ],
        params
      )

      // 生成预期积分佣金log
      queueForCommit(this.app, 'order', 'settleByOrder', [{oid: order._id, status: 0}], params)
    }
    const patchData: any = {
      status,
      paid,
      links,
    }

    const incomeStatus = isIncomePending(order.type, status, order.price)
    if (incomeStatus) {
      patchData.incomeStatus = incomeStatus
    }

    const {cashRefund, giftCardRefund} = calculateRefundBreakUp(
      {
        price: order.price,
        giftCard: order.priceBreakdown?.giftCard,
        cash: order.priceBreakdown?.cash,
        refund: order.refund,
      },
      refundPrice
    )
    // throw new Error('Error before patchData')
    // cash refund
    if (cashRefund > 0 && refundInfo.invalidLinks?.length > 0) {
      patchData.refundRequired = {
        amount: cashRefund,
        invalidLinks: refundInfo.invalidLinks,
        giftCardRefunded: giftCardRefund,
      }
      // process refund using external api after transaction is commited
      queueForCommit(
        this.app,
        'order',
        'processRefund',
        [order._id, {amount: refundInfo.refundPrice, invalidLinks: refundInfo.invalidLinks, refundLinkName, refundLinkCover, giftCardRefunded: giftCardRefund}],
        params
      )
    }
    // gift card refund
    if (giftCardRefund > 0) {
      patchData.$push = {refund: {method: 'giftCard', amount: giftCardRefund, executed: true, status: status, createdAt: new Date(), executedAt: new Date()}}

      await this.app.service('gift-card-log').createGiftCardLog(
        {
          uid: order.buyer,
          tab: 'earn',
          source: 'order',
          category: 'order_failed_refund',
          value: giftCardRefund,
          businessId: order._id.toString(),
          isSchool: order.isSchool,
        },
        params
      )
    }
    // deduct reserved giftcard balance (payment confirmed, no log needed)
    if (order.priceBreakdown?.giftCard > 0) {
      await this.app.service('gift-card-log').deductReservedBalance(order.buyer, isSchool, order.priceBreakdown.giftCard, transactionParams)
    }

    if (giftCardRefund > 0 && cashRefund <= 0) {
      queueForCommit(
        this.app,
        'order',
        'sendRefundSuccessNotification',
        [{order, amount: 0, refundLinkName, refundLinkCover, giftCardRefunded: giftCardRefund}],
        params
      )
    }
    // throw new Error('Error after releaseReservedBalance')
    // 更新订单
    await this.app.service('order').patch(order._id, patchData, transactionParams)
  }

  // 服务包购买后数据处理
  async completeService(insertData: any, user: any, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    let {packId, order, total, price, giftCount, point, isPoint, session, isPromotion, mentorPack, sectionCount} = insertData
    let isNew = false
    let packUser: any = mentorPack ? null : await this.app.service('service-pack-user').Model.findOne({uid: user._id, 'snapshot._id': packId}, null, options)
    if (!packUser) {
      isNew = true
      let buyData: any = {
        packId,
        order,
        price,
      }
      if (session) {
        buyData.session = session
      }
      if (mentorPack) {
        buyData.mentorPack = mentorPack
        buyData.sectionCount = sectionCount
      }
      packUser = await this.app.service('service-pack-user').buyByOrder(buyData, Acan.mergeTxnParams(params, {user}))
    }

    let payMethod = 'cash'
    if (isPoint) {
      payMethod = 'point'
    }
    if (total > 0) {
      await this.app.service('service-pack-user-data').add(
        {
          packUser: packUser._id,
          type: payMethod,
          times: total,
          payMethod,
          isNew,
          order,
          isPromotion,
        },
        Acan.mergeTxnParams(params, {user})
      )
      isNew = false
    }
    if (giftCount) {
      await this.app.service('service-pack-user-data').add(
        {
          packUser: packUser._id,
          type: 'gift',
          times: giftCount,
          payMethod: 'gift',
          isNew,
          order,
          isPromotion,
        },
        Acan.mergeTxnParams(params, {user})
      )
    }
    return packUser
  }
  // 服务包购买后数据处理 代课服务 线下
  async completeServiceSubstitute(insertData: any, user: any, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    let {packId, order, total, price, giftCount, point, isPoint, session, isPromotion, country, city, isOnCampus} = insertData
    let isNew = false
    let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: user._id, 'snapshot._id': packId, pid: {$exists: false}}, null, options)
    if (!packUser) {
      isNew = true
      let buyData: any = {
        packId,
        order,
        price,
      }
      if (session) {
        buyData.session = session
      }
      packUser = await this.app.service('service-pack-user').buyByOrder(buyData, Acan.mergeTxnParams(params, {user}))
    }
    if (isOnCampus) {
      var onCampusData: any = await this.app
        .service('service-pack-user')
        .substituteCreate({packId, pid: packUser._id, order, country, city, isPoint}, Acan.mergeTxnParams(params, {user}))
      isNew = onCampusData.isNew
    }

    let payMethod = 'cash'
    if (isPoint) {
      payMethod = 'point'
    }

    await this.app.service('service-pack-user-data').addSubstitute(
      {
        packUser: isOnCampus ? onCampusData.packUserSubstitute._id : packUser._id,
        isNew,
        order,
        payMethod,
        type: 'cash',
        times: Number(((total + giftCount) * 60).toFixed(0)),
      },
      Acan.mergeTxnParams(params, {user})
    )
    return packUser
  }
  // 主题服务包购买后数据处理
  async completeServicePremium(insertData: any, user: any, params?: TxnParams) {
    const options = Acan.getTxnOptions(params)
    let {packId, premium, order, total, price, giftCount, point, isPoint, session, isPromotion, packUserTasks = [], oldPackUser} = insertData
    let isNew = false
    let packUser: any = await this.app.service('service-pack-user').Model.findOne({uid: user._id, 'snapshot._id': packId}, null, options)
    if (!packUser) {
      isNew = true
      let buyData: any = {
        packId,
        order,
        price,
      }
      packUser = await this.app.service('service-pack-user').buyByOrder(buyData, Acan.mergeTxnParams(params, {user}))
    }

    let packUserPremium: any
    if (oldPackUser) {
      packUserPremium = await this.app.service('service-pack-user').Model.findOne({_id: oldPackUser}, null, options)
    } else {
      packUserPremium = await this.app.service('service-pack-user').Model.findOne({uid: user._id, pid: packUser._id.toString(), premium, order}, null, options)
    }
    if (!packUserPremium) {
      const pack: any = await this.app.service('service-pack').Model.findById(packId, null, options)
      await this.app.service('service-pack-user').contentOrientatedCreate(
        {
          order,
          pid: packUser._id,
          contentOrientated: pack.contentOrientated,
          isPoint,
          servicePremium: packId,
        },
        Acan.mergeTxnParams(params, {user})
      )
      packUserPremium = await this.app.service('service-pack-user').Model.findOne({uid: user._id, pid: packUser._id.toString(), premium, order}, null, options)
    }

    let payMethod = 'cash'
    if (isPoint) {
      payMethod = 'point'
    }
    if (total > 0) {
      let addData: any = {
        packUser: packUserPremium._id,
        type: payMethod,
        times: total,
        payMethod,
        isNew: true,
        order,
        isPromotion,
      }
      if (packUserTasks.length > 0) {
        addData.packUserTasks = packUserTasks
      }
      await this.app.service('service-pack-user-data').add(addData, Acan.mergeTxnParams(params, {user}))
    }
    if (giftCount) {
      let addData: any = {
        packUser: packUserPremium._id,
        type: 'gift',
        times: giftCount,
        payMethod: 'gift',
        isNew: true,
        order,
        isPromotion,
      }
      if (packUserTasks.length > 0) {
        addData.packUserTasks = packUserTasks
      }
      await this.app.service('service-pack-user-data').add(addData, Acan.mergeTxnParams(params, {user}))
    }
    return packUser
  }

  // 计算服务包使用数量,可退次数
  async calcServiceCount(
    {
      packUser,
      orderId,
      orderDoc,
      linkItem,
      serviceTicket = '',
    }: {
      packUser: any
      orderId: any
      orderDoc?: any
      linkItem?: {id: string; style: string; goods?: any}
      serviceTicket?: any
    },
    params?: TxnParams
  ) {
    const options = Acan.getTxnOptions(params)
    let query: any = {
      packUser: packUser._id,
      order: orderId,
    }
    if (serviceTicket) {
      query.serviceTicket = serviceTicket
    }
    let packUserData = await this.app.service('service-pack-user-data').Model.find(query, null, options)
    let refundCount = 0
    let unused = 0
    let used = 0
    let unusedCash = 0
    let unusedPoint = 0
    let unusedGift = 0
    let usedDoc: any
    for (let i = 0; i < packUserData.length; i++) {
      const item: any = packUserData[i]
      if (item.status == 0) {
        unused++
        if (item.payMethod != 'gift') {
          refundCount++
        }
        if (item.payMethod == 'cash') {
          unusedCash++
        }
        if (item.payMethod == 'point') {
          unusedPoint++
        }
        if (item.payMethod == 'gift') {
          unusedGift++
        }
      } else {
        used++
        usedDoc = item
      }
    }

    // First item free logic: if user only used 1 item and meets conditions, make it free
    if (used === 1 && orderDoc && !orderDoc.isSchool && linkItem && usedDoc && usedDoc.payMethod !== 'gift') {
      let shouldApplyFirstItemFree = false
      if (linkItem.style === 'service' && linkItem.goods?.type === 'mentoring' && linkItem.goods?.serviceRoles === 'mentoring') {
        const firstSuccessfulOrder = await this.Model.findOne(
          {
            buyer: orderDoc.buyer,
            status: {$in: [200, 500]},
            'links.id': linkItem.id,
          },
          null,
          {
            ...options,
            sort: {createdAt: 1}, // Get the earliest order
          }
        ).lean()

        // If the first successful order is the current order, apply benefit
        if (firstSuccessfulOrder && firstSuccessfulOrder._id.toString() === orderId.toString()) {
          shouldApplyFirstItemFree = true
        }
      } else if (linkItem.style === 'service_premium') {
        // For service_premium, only consider this for the first service_premium link in order.links
        const firstServicePremiumLink = orderDoc.links.find((link: any) => link.style === 'service_premium')

        // Only apply if this is the first service_premium link in the order
        if (firstServicePremiumLink && firstServicePremiumLink.id === linkItem.id) {
          const firstSuccessfulOrder = await this.Model.findOne(
            {
              buyer: orderDoc.buyer,
              status: {$in: [200, 500]},
              servicePremium: orderDoc.servicePremium,
            },
            null,
            {
              ...options,
              sort: {createdAt: 1}, // Get the earliest order
            }
          ).lean()

          // If the first successful order is the current order, apply benefit
          if (firstSuccessfulOrder && firstSuccessfulOrder._id.toString() === orderId.toString()) {
            shouldApplyFirstItemFree = true
          }
        }
      }

      // Apply the first item free benefit
      if (shouldApplyFirstItemFree) {
        refundCount++
      }
    }

    return {
      refundCount,
      unused,
      used,
      unusedCash,
      unusedPoint,
      unusedGift,
    }
  }
  // 计算服务包使用数量,可退次数 代课
  async calcServiceCountSubstitute(packUser: any, link: any, isPoint: boolean) {
    let remaining = packUser.total - packUser.used
    let {giftCount, count} = link
    giftCount = giftCount * 60
    count = count * 60
    let refundCount = 0
    let unused = 0
    let used = 0
    let unusedCash = 0
    let unusedPoint = 0
    let unusedGift = 0
    if (remaining >= count + giftCount) {
      refundCount = count
      unused = count + giftCount
      used = 0
      unusedCash = isPoint ? 0 : refundCount
      unusedPoint = isPoint ? refundCount : 0
      unusedGift = giftCount
    } else if (remaining < count + giftCount) {
      refundCount = remaining - giftCount > 0 ? remaining - giftCount : 0
      unused = remaining
      used = count + giftCount - remaining
      unusedCash = isPoint ? 0 : refundCount
      unusedPoint = isPoint ? refundCount : 0
      unusedGift = remaining - refundCount
    }
    return {
      refundCount,
      unused,
      used,
      unusedCash,
      unusedPoint,
      unusedGift,
    }
  }
  // 判断是否已有Promotion
  async checkPromotionExist(buyer: string, linkId: string) {
    // let exist = await this.Model.find({
    //   buyer: buyer,
    //   status: {$in: [100, 200]},
    //   links: {$elemMatch: {id: linkId, promotion: true, removed: {$exists: false}}},
    // })
    let exist = await this.Model.find({
      buyer: buyer,
      links: {$elemMatch: {id: linkId, promotion: true}},
    })
    return exist.length > 0
  }
  // 一个月内购买了几次Promotion
  async getCountPromotionByMonth({buyer}: any) {
    let exist = await this.Model.find({
      buyer: buyer,
      status: {$in: [100, 200]},
      links: {$elemMatch: {promotion: true, removed: {$exists: false}}},
      createdAt: {
        $gt: new Date(new Date().setMonth(new Date().getMonth() - 1)),
        $lt: new Date(),
      },
    })
    return {count: exist.length}
  }
  // 该用户已购买的Promotion服务包id 替代users.freeServiceType
  async getPromotionServiceId({buyer}: any) {
    let orders = await this.Model.find({
      buyer: buyer,
      status: {$in: [100, 200]},
      links: {$elemMatch: {promotion: true, removed: {$exists: false}}},
    })
    let freeServiceType: any = {}
    for (let i = 0; i < orders.length; i++) {
      const order: any = orders[i]
      if (order.servicePremium) {
        freeServiceType[order.servicePremium] = order._id
      } else {
        for (let j = 0; j < order.links.length; j++) {
          const link: any = order.links[j]
          if (link.promotion) {
            freeServiceType[link.id] = order._id
          }
        }
      }
    }
    return {freeServiceType}
  }
  /**
   * 查询已购买过的service,用于过滤领取免费服务包和session捆绑服务包.
   * 替代getPromotionServiceId部分场景
   */
  async getOrderedService({buyer}: any) {
    let orders = await this.Model.find({buyer: buyer, status: {$ne: 400}, 'links.style': {$in: ['service', 'service_premium']}})
    let serviceIds: any = new Set()
    for (let i = 0; i < orders.length; i++) {
      const order: any = orders[i]
      for (let j = 0; j < order.links.length; j++) {
        const link: any = order.links[j]
        if (link.style == 'service' || link.style == 'service_premium') {
          serviceIds.add(link.id)
        }
        if (link.style == 'service_premium') {
          serviceIds.add(order.servicePremium)
        }
      }
    }
    return {serviceIds: Array.from(serviceIds)}
  }
  // 根据booking查询订单
  async getPromotionByBooking({booking}: any) {
    let bookingInfo: any = await this.app.service('service-booking').Model.findOne({_id: booking})
    let {packUserData} = bookingInfo
    let userDataInfo = await this.app.service('service-pack-user-data').Model.find({_id: {$in: packUserData}})
    let orderIds = userDataInfo.map((e: any) => e.order)
    let orders = await this.Model.find({_id: {$in: orderIds}})

    let freeServiceType: any = {}
    for (let i = 0; i < orders.length; i++) {
      const order: any = orders[i]
      if (order.servicePremium) {
        freeServiceType[order.servicePremium] = order._id
      } else {
        for (let j = 0; j < order.links.length; j++) {
          const link: any = order.links[j]
          if (link.promotion) {
            freeServiceType[link.id] = order._id
          }
        }
      }
    }
    return {freeServiceType}
  }
  // 认证精品课快照 未使用查询 (弃用 改为直接find查询)
  // async getPremiumCloudUnused({}: any, params: Params) {
  //   const buyer = params?.user?._id
  //   let list: any = await this.Model.find({buyer, 'links.style': 'premium_cloud', 'links.premiumCloudUnused': true}).lean()
  //   let unused = []
  //   for (let i = 0; i < list.length; i++) {
  //     let {links} = list[i]
  //     for (let j = 0; j < links.length; j++) {
  //       let link = links[j]
  //       if (link.style == 'premium_cloud' && link.premiumCloudUnused) {
  //         link.serviceAuth = link.id
  //         unused.push(link)
  //       }
  //     }
  //   }
  //   return unused
  // }

  // 未支付提醒,超时前15min
  unpaidRemind() {
    this.Model.find({status: 100, reminder: {$lt: 1}, expiration: {$lt: Date.now() + 15 * 60 * 1000, $gt: Date.now() + 1 * 60 * 1000}}).then(
      async (rs: any) => {
        for (let i = 0; i < rs.length; i++) {
          const order = rs[i]
          let {links} = order
          const user = await this.app.service('users').uidToInfo(order.buyer)
          this.app.service('notice-tpl').send(
            'OrderUnpaidRemind',
            {_id: user._id, email: user.email},
            {
              username: user.name.join(' '),
              url: `${SiteUrl}/v2/order/detail/${order._id}`,
              name: `${links[0].name}${links.length > 1 ? ' +' + (links.length - 1) : ''}`,
            }
          )
          await this.Model.updateOne({_id: order._id}, {$set: {reminder: 1}})
        }
      }
    )
  }
  // 超时自动取消
  timeoutClose() {
    this.Model.find({status: 100, expiration: {$lt: Date.now()}}).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const order = rs[i]
        let links = order.links.map((e: any) => {
          e.removed = true
          return e
        })
        await this.Model.updateOne({_id: order._id}, {status: 400, links, $unset: {stripeId: ''}})

        // Cancel open stripe payment intent as the order is canceled
        this.app.service('stripe').cancelPaymentIntent({paymentIntentId: order.stripeId})

        const user = await this.app.service('users').uidToInfo(order.buyer)
        let url = `${SiteUrl}/v2/order/detail/${order._id}`
        this.app
          .service('notice-tpl')
          .send(
            'OrderTimeout',
            {_id: user._id, email: user.email},
            {url, nickname: user.nickname, name: `${order.links[0].name}${order.links.length > 1 ? ' +' + (order.links.length - 1) : ''}`}
          )
      }
    })
  }
  // braintree订单settled查询处理
  paymentConfirm(params: any) {
    this.Model.find({status: {$in: [200, 500, 501, 502, 503]}, settled: false, payMethod: {$in: ['braintree']}}).then(async (rs: any) => {
      for (let i = 0; i < rs.length; i++) {
        const order = rs[i]

        let detail: any = await this.app.service('braintree').get('detail', {query: {id: order.braintreeId}})
        if (detail.status === 'settled') {
          if (order.refund.length > 0) {
            await this.app.service('order').dispatchRefund(order, params)
          }
          this.app.service('order').patch(order._id, {settled: true})
        }
      }
    })
  }

  // 积分/佣金分账结算
  // type: {type: String, enum: ['unit', 'session_public', 'session_self_study', 'session_service_pack', 'service_pack']},
  autoSeparate() {
    this.Model.find({status: {$in: [200, 500, 501, 502, 503]}, settled: true, isSeparated: false, $or: [{isSchool: false}, {isSchool: {$exists: false}}]}).then(
      async (rs: any) => {
        for (let i = 0; i < rs.length; i++) {
          const order = rs[i]
          if (order.status == 200) {
            let {separateAllowed} = await this.getOrderRefundCheck({id: order._id, status: 500})
            if (!separateAllowed) {
              continue
            }
          }
          await this.settleByOrder({oid: order._id, status: 1})
          await this.Model.updateOne({_id: order._id}, {$set: {isSeparated: true}})
        }
      }
    )
  }

  async cron1({}: any, params?: Params): Promise<any> {
    this.unpaidRemind()
    this.timeoutClose()
    this.paymentConfirm(params)
    this.autoSeparate()
    this.orderRecovery.retryFailedRefunds()
    this.incomeProcessor.processIncomeCron()
  }

  // for testing cron
  async getIncome() {
    await this.incomeProcessor.processIncomeCron()
    return {success: true}
  }

  // 执行一次 links.gift=true 更新为 links.promotion=true
  async getUpdateGiftToPromotion() {
    let order = await this.Model.find({'links.gift': true})
    for (let i = 0; i < order.length; i++) {
      const item: any = order[i]
      let links = item.links.map((e: any) => {
        if (e.gift) {
          e.promotion = true
        }
        return e
      })
      await this.Model.updateOne({_id: item._id}, {links})
    }
  }

  // 去除逻辑  更新为谁分享的就给谁结算,和跟踪列表无关
  // async handleInviter({inviter, buyer, servicePack}: any) {
  //   let user: any = await this.app.service('users').Model.findOne({inviteCode: inviter})
  //   let servicerConfData: any = await this.app.service('service-conf').Model.findById(user._id)
  //   let {managerRoles = []} = user
  //   if (managerRoles.includes('sales') || managerRoles.includes('sales_manager') || servicerConfData?.serviceRoles?.includes('consultant')) {
  //     let salesFollowData: any = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack})
  //     // let salesFollowData: any = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack, type: 'following'})
  //     let inviterUid = salesFollowData ? salesFollowData.sales : ''
  //     if (inviterUid) {
  //       let inviterUser: any = await this.app.service('users').Model.findOne({_id: inviterUid})
  //       return inviterUser.inviteCode
  //     } else {
  //       return inviter
  //     }
  //   } else {
  //     return inviter
  //   }
  // }

  // prompt,premium_cloud使用 更新used
  async getUsePrompt({orderId, prompt}: any) {
    return await this.Model.updateOne({_id: orderId, links: {$elemMatch: {id: prompt, used: false}}}, {$set: {'links.$.used': true}})
  }
  // 判断订单type
  async getOrderType({links, servicePremium}: any) {
    let type = ''
    let hasUnit = links.some((item: any) => item.style === 'unit')
    let hasSession = links.some((item: any) => item.style === 'session')
    let hasSelfStudy = links.some((item: any) => item.goods.type === 'selfStudy')
    let hasService = links.some((item: any) => item.style === 'service')
    let hasServiceSubstitute = links.some((item: any) => item.style === 'service_substitute')
    let hasPrompt = links.some((item: any) => item.style === 'prompt')
    let hasPremiumCloud = links.some((item: any) => item.style === 'premium_cloud')
    let hasGiftCard = links.some((item: any) => item.style === 'gift_card')
    let hasSection = links.some((item: any) => item.style === 'section_top_up')
    let hasRemainingSections = links.some((item: any) => item.style === 'remaining_sections')

    if (hasRemainingSections) {
      type = 'remaining_sections'
    }
    if (hasSection) {
      type = 'section_top_up'
    }
    if (hasGiftCard) {
      type = 'gift_card'
    }
    if (hasUnit) {
      type = 'unit'
    }
    if (hasSession && !hasService && !hasSelfStudy) {
      type = 'session_public'
    }
    if (hasSession && !hasService && hasSelfStudy) {
      type = 'session_self_study'
    }
    if (hasService && !hasSession) {
      type = 'service_pack'
    }
    if (hasServiceSubstitute && !hasSession) {
      type = 'service_substitute'
    }
    if (hasService && hasSession) {
      type = 'session_service_pack'
    }
    if (servicePremium) {
      type = 'service_premium'
    }
    if (hasPremiumCloud) {
      type = 'premium_cloud'
    }
    if (hasPrompt) {
      type = 'prompt'
    }
    return type
  }

  addLogStorage({logs, item}: any) {
    if (!logs[`${item.uid}-${item.category}-${item.type}-${item.isSchool}`]) {
      logs[`${item.uid}-${item.category}-${item.type}-${item.isSchool}`] = item
    } else {
      logs[`${item.uid}-${item.category}-${item.type}-${item.isSchool}`].change += item.change
    }
  }
  // 结算订单的积分/佣金
  async settleByOrder({oid, status = 1}: any) {
    let order: any = await this.app.service('order').Model.findById(oid)
    if (!order) {
      return
    }
    if (order.price == 0 && order.point == 0) {
      return
    }
    let {_id, buyer, type, links, servicePremium, isPoint, schoolInviter, inviteSource, inviteSourceId} = order

    let logs: any = {}
    for (let j = 0; j < links.length; j++) {
      const goods = links[j]
      let remainingPrice = Number((goods.price - goods.refundPrice).toFixed(0))
      let remainingPoint = Number((goods.point - goods.refundPoint).toFixed(0))

      if (remainingPrice <= 0 && remainingPoint <= 0) {
        continue
      }

      // 商品作者
      let authorUid
      if (goods.style == 'unit') {
        // let unitData: any = await this.app.service('unit').Model.findOne({_id: goods.id})
        let unitData: any = goods.goods
        authorUid = unitData.uid
      } else if (goods.style == 'session') {
        // let sessionData: any = await this.app.service('session').Model.findOne({_id: goods.id})
        let sessionData: any = goods.goods
        authorUid = sessionData.uid
      }
      // 积分购买 结算积分 给unit/session原作者
      if (isPoint) {
        if (goods.style != 'unit' && goods.style != 'session') {
          continue
        }
        let resPoint = await this.app
          .service('point-setting')
          .calcPoint({type: {category: 'points_purchase'}, amount: remainingPoint, tab: 'earn', isPoint: false})

        this.addLogStorage({
          logs,
          item: {
            uid: authorUid,
            tab: 'earn',
            category: 'points_purchase',
            change: resPoint.point,
            businessId: _id,
            snapshot: order,
            type: 'point',
            isSchool: false,
          },
        })
      } else {
        // 现金够买
        let inviteCode = goods.inviter || order.inviter
        let schoolInviter = goods.schoolInviter || order.schoolInviter
        let inviteSource = goods.inviteSource || order.inviteSource
        let inviteSourceId = goods.inviteSourceId || order.inviteSourceId
        if (!inviteCode) {
          continue
        }
        let inviteUser: any = await this.app.service('users').Model.findOne({inviteCode})

        if (!inviteUser) {
          // 邀请码匹配不到人 直接跳过
          await this.Model.updateOne({_id: _id}, {$set: {isSeparated: true}})
          continue
        }

        // 分享人为商品作者,不结算
        if (inviteUser._id == authorUid) {
          continue
        }

        let isCommission = false
        let commissionRole = '' // commission-setting.role
        let isSchoolAmbassador = await this.app.service('ambassador-auth').isSchoolAmbassador(inviteUser._id)
        let isAgent = await this.app.service('manager').isAgent(inviteUser._id)
        if (isAgent) {
          commissionRole = 'agency'
        }
        // ambassador
        if (!isAgent && isSchoolAmbassador && ['session', 'service', 'service_premium'].includes(goods.style)) {
          isCommission = true
          commissionRole = 'ambassador_school'
        }

        if (inviteSource == 'sales_follow_up') {
          // 销售跟踪 从销售跟踪分享的 且当前仍在跟踪
          let salesFollowData: any
          if (type == 'service_premium') {
            salesFollowData = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack: servicePremium})
          } else if (goods.style == 'service' || goods.style == 'service_substitute') {
            salesFollowData = await this.app.service('sales-follow-up').Model.findOne({customer: buyer, servicePack: goods.id})
          }
          let inviterUid = salesFollowData ? salesFollowData.sales : ''
          if (inviterUid) {
            if (inviterUid == inviteUser._id) {
              isCommission = true
              commissionRole = salesFollowData.salesType == 'manager' ? 'classcipe_staff' : 'education_consultant'
            } else {
              if (!isAgent && isSchoolAmbassador && ['session', 'service', 'service_premium'].includes(goods.style)) {
                isCommission = true
                commissionRole = 'ambassador_school'
              }
            }
          }
        }
        if (inviteSource == 'new_prompt') {
          let sourceSession: any = await this.app.service('session').Model.findOne({_id: inviteSourceId})
          // 个人课堂
          if (sourceSession.personal) {
            if (sourceSession?.students.length == 0) {
              // workshop
              isCommission = true
              // ambassador
              if (!isAgent) {
                if (isSchoolAmbassador) {
                  commissionRole = 'ambassador_school'
                } else {
                  commissionRole = 'ambassador_general'
                }
              }
            } else {
              // 非workshop
              // ambassador
              if (!isAgent) {
                if (isSchoolAmbassador) {
                  isCommission = true
                  commissionRole = 'ambassador_school'
                }
              }
            }
          }
          // 学校课堂
          if (!sourceSession.personal) {
            schoolInviter = sourceSession.school
          }
        }
        if (schoolInviter) {
          commissionRole = 'organization'
        }
        if (isAgent || schoolInviter || isCommission) {
          // 结算佣金
          let category = goods.style
          if (order.type == 'session_self_study' && goods.style == 'session') {
            category = 'self_study'
          }
          let resCommission = await this.app
            .service('commission-setting')
            .calcCommission({type: {category}, amount: remainingPrice, tab: 'earn', role: commissionRole})
          this.addLogStorage({
            logs,
            item: {
              uid: schoolInviter || inviteUser._id,
              tab: 'earn',
              source: 'reward',
              category: category,
              change: resCommission.commission,
              businessId: _id,
              snapshot: order,
              type: 'commission',
              isSchool: schoolInviter ? true : false,
              role: commissionRole,
            },
          })
          if (schoolInviter) {
            // 学校分享 给学校佣金 个人积分
            let resPoint = await this.app.service('point-setting').calcPoint({type: {category}, amount: remainingPrice, tab: 'earn', isPoint: false})
            this.addLogStorage({
              logs,
              item: {
                uid: inviteUser._id,
                tab: 'earn',
                source: 'reward',
                category: category,
                change: resPoint.point,
                businessId: _id,
                snapshot: order,
                type: 'point',
                isSchool: false,
              },
            })
          }
        } else {
          // 结算积分
          let category = goods.style
          if (order.type == 'session_self_study' && goods.style == 'session') {
            category = 'self_study'
          }
          let resPoint = await this.app.service('point-setting').calcPoint({type: {category}, amount: remainingPrice, tab: 'earn', isPoint: false})

          if (resPoint.success) {
            this.addLogStorage({
              logs,
              item: {
                uid: inviteUser._id,
                tab: 'earn',
                source: 'reward',
                category: category,
                change: resPoint.point,
                businessId: _id,
                snapshot: order,
                type: 'point',
                isSchool: false,
              },
            })
          }
        }
      }
    }

    for (let key in logs) {
      await this.app.service('point-log').handleAddLog({
        ...logs[key],
        status,
      })
    }
  }
}
