import {GeneralError} from '@feathersjs/errors'
import {Application} from '../../../declarations'
import {shouldExecute} from '../../../hooks/cronScheduler'
import {commitTransactionSession, rollbackTransactionSession, startTransactionSession, TxnParams} from '../../../hooks/dbTransactions'
import {calculateRefundBreakUp} from './priceUtils'

declare const SiteUrl: string
declare const hashToUrl: (hash: string) => string

interface ProcessLinksCustomData {
  links: any[]
  user: any
  isOrderCreation: boolean
  dict?: any
  serviceAuthDict?: any
  serviceSessionId?: string
  invalidLinks?: any[]
  invalidLinksId?: string[]
}

export class OrderRecovery {
  adminEmails: string[]
  maxRetryCount: number
  constructor(private app: Application) {
    this.adminEmails = ['<EMAIL>']
    this.maxRetryCount = 3
  }

  /**
   * Main entry point for order completion, after successful payment.
   * @param orderId The ID of the order to process.
   * @param paymentDetails Payment information, only used on the first API call.
   * @param source The source of the call: 'api' or 'webhook'.
   */
  async processOrderCompletion(orderId: string, paymentDetails: any, source: 'api' | 'webhook') {
    const lockDurationMinutes = 3
    const lockExpiresAt = new Date(Date.now() + lockDurationMinutes * 60 * 1000)

    const originalOrder: any = await this.app
      .service('order')
      .Model.findOneAndUpdate(
        {
          _id: orderId,
          status: {$in: [100, 110]},
          $or: [{processingLockExpiresAt: {$exists: false}}, {processingLockExpiresAt: {$lt: new Date()}}],
        },
        {
          $set: {
            processingLockExpiresAt: lockExpiresAt,
            status: 110,
          },
        }
        // We do NOT use { new: true }, so we get the document *before* this update.
      )
      .lean()
    if (!originalOrder) {
      if (source === 'webhook') {
        const orderInfo = (await this.app.service('order').Model.findById(orderId).select('status')) as any
        if ([100, 110].includes(orderInfo?.status)) throw new Error(`Order ${orderId} is locked. Webhook will retry.`)
      }
      return
    }

    let orderToProcess = originalOrder

    // This is only done if this is the first time the function is running for this order.
    if (!originalOrder.settled) {
      // this triggers a websocket event to notify the frontend
      orderToProcess = await this.app.service('order').patch(orderId, paymentDetails)
    }
    if (source === 'api') {
      // when calling via REST api, we don't wait for the detailed processing to finish
      // this is done to show the user quick response that the payment was successful, and the order is processing
      this.detailedProcessing(orderToProcess)
    } else {
      await this.detailedProcessing(orderToProcess)
    }
  }

  async detailedProcessing(order: any) {
    const currentRetryCount = order.retryInfo?.count || 0
    const orderId = order._id
    const session = await startTransactionSession(this.app)
    let refundInfo: any = null

    try {
      const transactionParams = {mongoose: {session}, sideEffectsToExecute: []}

      refundInfo = await this.app.service('order').completeOrder(order, transactionParams)
      await commitTransactionSession(session, this.app, transactionParams)
    } catch (error: any) {
      // console.log('Error during order processing:', error)
      await rollbackTransactionSession(session)

      // do the updates like gift card refund here, so that if fails can be retried again.

      // --- Error Analysis ---
      let decision = 'REFUND' // Default to refund for safety
      let retryReason = ''

      if (error?.hasErrorLabel && (error.hasErrorLabel('TransientTransactionError') || error.hasErrorLabel('UnknownTransactionCommitResult'))) {
        decision = 'RETRY'
        retryReason = error.hasErrorLabel('TransientTransactionError') ? 'TransientTransactionError' : 'UnknownTransactionCommitResult'
        retryReason += ' - ' + error?.message
      } else if (['ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED'].includes(error?.code)) {
        decision = 'RETRY'
        retryReason = 'NetworkIssue - ' + error?.message
      } else {
        decision = 'REFUND'
      }

      if (decision === 'RETRY' && currentRetryCount < this.maxRetryCount) {
        await this.app
          .service('order')
          .Model.updateOne({_id: orderId}, {$inc: {'retryInfo.count': 1}, $set: {'retryInfo.reason': retryReason, 'retryInfo.updatedAt': new Date()}})
        throw error // Re-throw the original error to trigger the webhook retry
      } else {
        // This block is reached for permanent errors OR if max retries have been exceeded.
        await this.handleFailedOrder(order)
      }
    } finally {
      this.releaseLock(orderId)
    }
  }

  async releaseLock(orderId: string) {
    try {
      await this.app.service('order').Model.updateOne({_id: orderId}, {$unset: {processingLockExpiresAt: ''}})
    } catch (error) {}
  }

  // Issue full refund for all products in the order
  async handleFailedOrder(order: any) {
    try {
      if (!Array.isArray(order.links)) return
      // Calculate total refund amount and collect product details
      let totalRefundAmount = 0
      const refundLinkName: string[] = []
      const refundLinkCover: string[] = []
      let myLinks = Acan.clone(order.links)
      for (const link of myLinks || []) {
        totalRefundAmount += link.price || 0
        if (link.name) {
          refundLinkName.push(link.name)
        }
        if (link.cover) {
          refundLinkCover.push(link.cover)
        }
        link.removed = true
        link.refundPrice = link.price
      }

      const {cashRefund, giftCardRefund} = calculateRefundBreakUp(
        {
          price: order.price,
          giftCard: order.priceBreakdown?.giftCard,
          cash: order.priceBreakdown?.cash,
          refund: order.refund,
        },
        totalRefundAmount
      )

      await this.processDbUpdate({order, myLinks, cashRefund, giftCardRefund, refundLinkName, refundLinkCover})

      if (cashRefund > 0) {
        const refundInfo = {
          amount: cashRefund,
          refundLinkName: refundLinkName,
          refundLinkCover: refundLinkCover,
          giftCardRefunded: giftCardRefund,
        }
        await this.processRefund(order._id, refundInfo, order)
      }
    } catch (error) {
      const currentRetryCount = order.retryInfo?.count || 0
      if (currentRetryCount < this.maxRetryCount) {
        throw error
      }
    }
  }
  /*
  - gift card refund amount in email
  */
  async processDbUpdate({order, updatedLinks, cashRefund, giftCardRefund, refundLinkName, refundLinkCover}: any) {
    const trxnParams: TxnParams = {}
    if (giftCardRefund > 0 || order.priceBreakdown?.giftCard > 0) {
      const session = await startTransactionSession(this.app)
      trxnParams.mongoose = {session}
    }
    try {
      const promises = []
      const patchData: any = {
        status: 504,
        paid: 2,
        links: updatedLinks,
      }
      if (cashRefund > 0) {
        patchData.refundRequired = {
          amount: cashRefund,
          invalidLinks: order.links,
        }
      }
      if (giftCardRefund > 0) {
        promises.push(
          this.app.service('gift-card-log').createGiftCardLog(
            {
              uid: order.buyer,
              tab: 'earn',
              source: 'order',
              category: 'order_failed_refund',
              value: giftCardRefund,
              businessId: order._id.toString(),
              isSchool: order.isSchool,
            },
            trxnParams
          )
        )
        patchData.$push = {refund: {method: 'giftCard', amount: giftCardRefund, executed: true, status: 503, createdAt: new Date(), executedAt: new Date()}}
      }
      if (order.priceBreakdown?.giftCard > 0) {
        promises.push(
          this.app.service('gift-card-log').releaseReservedBalance(order.buyer, order.isSchool, order.priceBreakdown.giftCard, order._id.toString(), trxnParams)
        )
      }
      promises.push(this.app.service('order').patch(order._id, patchData, trxnParams))
      await Promise.all(promises)
      await commitTransactionSession(trxnParams.mongoose?.session, this.app, trxnParams)
      if (giftCardRefund > 0 && cashRefund <= 0) {
        this.sendRefundSuccessNotification({order, amount: 0, refundLinkName, refundLinkCover, giftCardRefunded: giftCardRefund})
      }
    } catch (error: any) {
      await rollbackTransactionSession(trxnParams.mongoose?.session)
      throw error
    }
  }

  // Process refund for invalid links
  async processRefund(orderId: string, refundInfo: any, orderDoc?: any, isRetry?: boolean) {
    try {
      const order: any = orderDoc || (await this.app.service('order').Model.findOne({_id: orderId}))
      if (!order) {
        return
      }
      const {amount, refundLinkName = [], refundLinkCover = [], giftCardRefunded = 0} = refundInfo

      if (amount <= 0) return

      let refundSuccess = false
      // throw new Error('Error in refund')
      // Process refund via payment provider
      if (order.payMethod.indexOf('paypal') > -1) {
        const refundResult: any = await this.app.service('paypal').get('refund', {
          query: {id: order.paypalId, amount: (amount / 100).toFixed(2)},
        })
        if (refundResult.success) {
          refundSuccess = true
        }
      } else if (order.payMethod.indexOf('stripe') > -1) {
        const refundResult: any = await this.app.service('stripe').createRefund({
          paymentIntentId: order.stripeId,
          amount: amount,
        })
        if (refundResult.status === 'succeeded') {
          refundSuccess = true
        }
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundSuccess = true
      }
      // throw new Error('Error after refund')
      if (refundSuccess) {
        await this.handleRefundSuccess(order, amount, refundLinkName, refundLinkCover, giftCardRefunded)
      } else {
        if (isRetry) throw new Error('Refund failed')
        await this.handleRefundFailure(orderId, amount, refundLinkName)
      }
    } catch (error) {
      if (isRetry) throw new Error('Refund failed')
      await this.handleRefundFailure(orderId, refundInfo.amount, refundInfo.refundLinkName)
    }
  }

  // Handle successful refund
  async handleRefundSuccess(order: any, amount: number, refundLinkName: string[], refundLinkCover: string[], giftCardRefunded: number) {
    try {
      let refundList: any = []
      let notifyCustomer = false

      if (order.payMethod.indexOf('paypal') > -1) {
        refundList.push({
          method: 'paypal',
          amount: amount,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: 503,
        })
        notifyCustomer = true
      } else if (order.payMethod.indexOf('stripe') > -1) {
        refundList.push({
          method: 'stripe',
          amount: amount,
          createdAt: new Date(),
          executedAt: new Date(),
          executed: true,
          status: 503,
        })
        notifyCustomer = true
      } else if (order.payMethod.indexOf('braintree') > -1) {
        refundList.push({
          method: 'braintree',
          amount: amount,
          createdAt: new Date(),
          executed: false,
          status: 503,
        })
      }
      await this.app.service('order').patch(order._id, {
        $push: {refund: {$each: refundList}},
        $unset: {refundRequired: ''},
      })

      if (notifyCustomer && refundLinkName?.length > 0) {
        // Send success notification to customer
        await this.sendRefundSuccessNotification({order, amount, refundLinkName, refundLinkCover, giftCardRefunded})
      }
    } catch (error) {}
  }

  // Handle failed refund
  async handleRefundFailure(orderId: string, amount: number, refundLinkName: string[]) {
    // Send admin notification for manual intervention
    await this.sendRefundFailureAdminNotification(orderId, amount)

    // Send customer notification about delay
    await this.sendRefundDelayCustomerNotification(orderId, refundLinkName)
  }

  // Send refund success notification to customer
  async sendRefundSuccessNotification({order, amount, refundLinkName, refundLinkCover, giftCardRefunded}: any) {
    try {
      const userId = order.isSchool ? order.schoolAdmin : order.buyer
      const user = await this.app.service('users').uidToInfo(userId)
      const url = `${SiteUrl}/v2/order/payHistory/${order._id}`
      const url2 = `${SiteUrl}/v2/order/detail/${order._id}`
      await this.app.service('notice-tpl').send(
        'OrderRefundSuccess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          gift_card_amount: (giftCardRefunded / 100).toFixed(2),
          cash_amount: (amount / 100).toFixed(2),
          no: order.no,
          amount: (order.price / 100).toFixed(2),
          date: order.paidAt ? new Date(order.paidAt) : new Date(),
          url: url,
          link_name: refundLinkName.join(', '),
          url2: url2,
          image: hashToUrl(refundLinkCover[0] || ''),
          addons: refundLinkName.length > 1 ? `+${refundLinkName.length - 1} products` : '',
        }
      )
    } catch (error) {}
  }

  // Send refund delay notification to customer
  async sendRefundDelayCustomerNotification(orderId: string, refundLinkName: string[]) {
    try {
      const order: any = await this.app.service('order').Model.findOne({_id: orderId})
      const userId = order.isSchool ? order.schoolAdmin : order.buyer
      const user = await this.app.service('users').uidToInfo(userId)
      const url = `${SiteUrl}/v2/order/detail/${orderId}`

      await this.app.service('notice-tpl').send(
        'OrderRefundInProcess',
        {_id: user._id, email: user.email},
        {
          username: user.name.join(' '),
          link_name: refundLinkName.join('<br>'),
          url: url,
        }
      )
    } catch (error) {}
  }

  // Send admin notification for refund failure
  async sendRefundFailureAdminNotification(orderId: string, amount: number) {
    try {
      await Promise.all(
        this.adminEmails.map(async (email) => {
          await this.app.service('notice-tpl').send(
            'OrderRefundFailed',
            {_id: '', email: email},
            {
              orderId: orderId,
              amount: amount,
              url: `${SiteUrl}/v2/order/detail/${orderId}`,
              sys_url: `${SiteUrl}/v2/sys/order-failure-logs`,
            }
          )
        })
      )
    } catch (error) {}
  }

  // Retry failed refunds - cron job method
  async retryFailedRefunds() {
    try {
      if (!shouldExecute(2, ['01:00', '15:00'])) return

      // Find orders with pending refunds (older than 10 minutes to avoid immediate retries)
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
      const ordersWithPendingRefunds = await this.app
        .service('order')
        .Model.find({
          'refundRequired.createdAt': {$lt: tenMinutesAgo},
          'refundRequired.escalated': false,
        })
        .sort({'refundRequired.createdAt': 1})
        .limit(50)
      for (const orderDoc of ordersWithPendingRefunds) {
        try {
          const order: any = orderDoc
          const refundInfo = {
            amount: order.refundRequired.amount,
            invalidLinks: order.refundRequired.invalidLinks,
            refundLinkName: order.refundRequired.refundLinkName || order.refundRequired.invalidLinks?.map((link: any) => link.name) || [],
            refundLinkCover: order.refundRequired.refundLinkCover || order.refundRequired.invalidLinks?.map((link: any) => link.cover) || [],
            giftCardRefunded: order.refundRequired.giftCardRefunded || 0,
          }
          // For PayPal orders, check if refund already exists before retrying
          if (order.paypalId) {
            try {
              const refundCheck = await this.app.service('paypal').checkRefundExists(order.paypalId)
              if (refundCheck.exists) {
                await this.handleRefundSuccess(order, refundInfo.amount, refundInfo.refundLinkName, refundInfo.refundLinkCover, refundInfo.giftCardRefunded)
                continue
              }
            } catch (error) {
              // Continue with retry if we can't check status
            }
          }
          await this.processRefund(order._id.toString(), refundInfo, orderDoc, true)
        } catch (error) {
          const order: any = orderDoc

          // If refund has been pending for more than 24 hours, escalate to admin
          const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
          if (order.refundRequired.createdAt < twentyFourHoursAgo) {
            await this.sendRefundFailureAdminNotification(order._id.toString(), order.refundRequired.amount)

            // Mark as escalated to prevent repeated admin notifications
            await this.app.service('order').Model.updateOne({_id: order._id}, {$set: {'refundRequired.escalated': true}})
          }
        }
      }
    } catch (error) {}
  }
  async processOrderLinks(customData: ProcessLinksCustomData, orderData: any, params?: TxnParams): Promise<any> {
    const {links, user, isOrderCreation, dict, serviceAuthDict, serviceSessionId, invalidLinks, invalidLinksId} = customData

    const {_id, isPoint, isSchool, buyer, servicePackApply, servicePremium, sharedSchool, persons, isTicket} = orderData

    const orderId = orderData._id.toString()

    const sellers: string[] = []

    const options = Acan.getTxnOptions(params)
    const transactionParams = Acan.mergeTxnParams(params)

    // Determines if the core processing logic should run.
    // In hooks, this depends on status 200 (for point purchases).
    // In completeOrder, it's always true as payment is confirmed.
    const shouldProcess = isOrderCreation ? orderData.status === 200 : true

    for (let i = 0; i < links.length; i++) {
      const item = links[i]
      // Logic from hook: Populate item details from pre-fetched data.
      if (isOrderCreation && dict && dict[item.id]) {
        const post = dict[item.id]
        item.name = post.name
        item.cover = post.cover || post.image
        if (post.uid && !sellers.includes(post.uid)) {
          sellers.push(post.uid)
        }
        if (item.style === 'service_premium' && serviceAuthDict && serviceAuthDict[item.id]) {
          const serviceAuth = serviceAuthDict[item.id]
          item.name = serviceAuth?.unit?.name || post.name
          item.cover = serviceAuth?.unitSnapshot?.cover || post.cover
        } else if (item.style === 'section_top_up') {
          item.cover = post.taskDetails.cover
        } else if (item.style === 'remaining_sections') {
          item.cover = post.snapshot.cover
        }
      }

      // logic from completeOrder
      if (!isOrderCreation && invalidLinksId?.includes(item.id)) {
        const invalidIndex = invalidLinksId.indexOf(item.id)
        if (invalidLinks) {
          links[i] = invalidLinks[invalidIndex]
          continue
        }
      }

      // ==========================================================================
      // main link processing logic starts here
      // ==========================================================================
      if (!shouldProcess) continue

      if (item.style === 'gift_card') {
        // only call from complete order, as gift cards are handled separately in completeOrder
        if (isOrderCreation) {
          throw new GeneralError('Invalid order for gift card')
        }
        const giftCardData = {
          amount: item.giftCardData.amount,
          isGift: item.giftCardData.isGift,
          recipientEmail: item.giftCardData.recipientEmail,
          senderName: item.giftCardData.senderName,
          recipientName: item.giftCardData.recipientName,
          giftMessage: item.giftCardData.giftMessage,
          image: item.giftCardData.image,
          order: orderId,
          isSchool: isSchool,
          school: isSchool ? buyer : undefined,
        }

        // Create the gift card
        const newGiftCardDoc = await this.app.service('gift-card').create(giftCardData, {user, ...transactionParams})
        item.id = newGiftCardDoc._id
      } else if (item.style === 'unit') {
        if (!isPoint) {
          await this.app.service('unit').patch(item.id, {$inc: {income: item.price}}, transactionParams)
        }
        const post = await this.app.service('unit').copy({_id: item.id, orderId: orderId}, {user: {_id: buyer}, ...transactionParams})
        item.newId = post._id
        item.hash = post.sid
      } else if (item.style === 'session') {
        console.log('user', user)
        const reg = {avatar: user.avatar, nickname: await this.app.service('users').nameFormatter({user}), _id: user._id.toString(), order: orderId}
        console.log('reg', reg)
        if (!isPoint) {
          await this.app.service('session').patch(item.id, {$inc: {income: item.price}}, Acan.mergeTxnParams(params, {user}))
        }
        const startDate = isOrderCreation ? new Date(dict[item.id].start).toString() : new Date(item.goods.start).toString()
        await this.app.service('session').patch(item.id, {_date: startDate, $addToSet: {reg}}, Acan.mergeTxnParams(params, {user}))
      } else if (item.style === 'service' || item.style === 'service_substitute') {
        const insertData: any = {
          packId: item.id,
          order: orderId,
          total: item.count,
          price: item.price,
          giftCount: item.giftCount,
          point: item.point,
          isPoint,
          isPromotion: item.promotion,
          ...(item.mentorPack ? {mentorPack: item.mentorPack} : {}),
          sectionCount: item.sectionCount,
        }
        if (isOrderCreation) item.sessionId = serviceSessionId
        if (item.sessionId) {
          insertData.session = item.sessionId
        }
        if (orderData.type === 'session_service_pack') {
          const sessionDoc: any = await this.app.service('session').Model.findOne({_id: item.sessionId}, null, options)
          if (sessionDoc.promotion) {
            insertData.giftCount = item.count + item.giftCount
            insertData.total = 0
          }
        }
        if (item.style === 'service_substitute') {
          if (item.isOnCampus) {
            insertData.country = item.country
            insertData.city = item.city
          }
          insertData.isOnCampus = item.isOnCampus
          await this.app.service('order').completeServiceSubstitute(insertData, user, transactionParams)
        } else {
          if (!isSchool) {
            await this.app.service('order').completeService(insertData, user, transactionParams)
          }
        }
      } else if (item.style === 'service_premium') {
        const insertData: any = {
          premium: item.id,
          packId: servicePremium,
          order: orderId,
          total: item.count,
          price: item.price,
          giftCount: item.giftCount,
          point: item.point,
          isPoint,
          isPromotion: item.promotion,
          packUserTasks: item.packUserTasks,
          ...(item.oldPackUser && {oldPackUser: item.oldPackUser}),
        }

        if (!isSchool) {
          // 从平台购买，完成主题服务包购买
          if (!sharedSchool) {
            await this.app.service('order').completeServicePremium(insertData, user, transactionParams)
          }
        } else {
          // 学校购买，完成主题服务包购买
          await this.app.service('order').completeServicePremium(insertData, {_id: buyer}, transactionParams)
        }
      } else if (item.style === 'premium_cloud') {
        const sessionDoc = await this.app
          .service('service-booking')
          .importByBooking({serviceAuthId: item.id, bookingId: item.bookingId, order: orderId}, {user, ...transactionParams})
        item.session = sessionDoc._id
        item.used = true
      } else if (item.style === 'section_top_up') {
        await this.app.service('service-pack-user').buyServiceTaskCredit({sectionId: item.id, amount: item.price, order: orderId}, transactionParams)
      } else if (item.style === 'remaining_sections') {
        await this.app.service('section').buyRemainingSections({packUserId: item.id, order: orderId}, {user, ...transactionParams})
      }
      if (!isPoint && ['service', 'service_substitute', 'section_top_up', 'remaining_sections'].includes(item.style)) {
        // 获取服务包信息
        const packId = item.style === 'section_top_up' ? item.goods.taskDetails.id : item.style === 'remaining_sections' ? item.goods.snapshot._id : item.id
        let pack: any = await this.app.service('service-pack').Model.findOne({_id: packId}, null, options).lean()
        let statistic = pack.statistic || []
        let premiumType = 'all'
        let isFind = statistic.find((e: any) => e.count == item.count && e.isSchool == isSchool && e.type == premiumType && e.city == item.city)
        if (isFind) {
          await this.app.service('service-pack').Model.updateOne(
            {
              _id: packId,
              statistic: {
                $elemMatch: {count: item.count, isSchool: isSchool, type: premiumType, city: item.city},
              },
            },
            {$inc: {'statistic.$.orderCount': 1, 'statistic.$.income': item.price}},
            options
          )
        } else {
          // 如果没找到匹配记录，添加新的统计记录
          await this.app
            .service('service-pack')
            .Model.updateOne(
              {_id: packId},
              {$push: {statistic: {count: item.count, orderCount: 1, income: item.price, isSchool, type: premiumType, city: item.city}}},
              options
            )
        }
        // 更新income
        await this.app.service('service-pack').Model.updateOne({_id: packId}, {$inc: {income: item.price}}, options)
      }
    }
    // Post-loop processing for the entire order
    if (!shouldProcess) return {links, sellers}

    if (servicePackApply) {
      await this.app.service('service-pack-apply').Model.updateOne({_id: servicePackApply}, {interviewOrder: orderId}, options).exec()
    }
    if (servicePremium) {
      await this.app.service('service-pack-apply').updateOrderInfo({uid: buyer, servicePack: servicePremium, sharedSchool, order: orderId}, transactionParams)
      if (!isPoint) {
        let pack: any = await this.app.service('service-pack').Model.findOne({_id: servicePremium}, null, options).lean()
        let statistic = pack.statistic || []
        let premiumType = 'all'
        if (isSchool) {
          // 获取类型
          console.log('links dd', links)
          premiumType = this.app.service('order').getServicePremiumType(links)
        }
        let isFind = statistic.find((e: any) => e.count == 1 && e.isSchool == isSchool && e.type == premiumType)
        if (isFind) {
          await this.app
            .service('service-pack')
            .Model.updateOne(
              {_id: servicePremium, statistic: {$elemMatch: {count: 1, isSchool: isSchool, type: premiumType}}},
              {$inc: {'statistic.$.orderCount': 1, 'statistic.$.income': orderData.price}},
              options
            )
        } else {
          await this.app
            .service('service-pack')
            .Model.updateOne(
              {_id: servicePremium},
              {$push: {statistic: {count: 1, orderCount: 1, income: orderData.price, isSchool, type: premiumType}}},
              options
            )
        }
        await this.app.service('service-pack').Model.updateOne({_id: servicePremium}, {$inc: {income: orderData.price}}, options)
      }
    }
    if (isTicket && servicePremium) {
      await this.app.service('service-pack-ticket').generate({persons, school: buyer, servicePremium, order: orderId, links}, transactionParams)
    }

    return {links, sellers}
  }
}
