import {Service, MongooseServiceOptions} from 'feathers-mongoose'
import {Application} from '../../declarations'
import {BadRequest, Forbidden, NotFound} from '@feathersjs/errors'
import {TxnParams} from '../../hooks/dbTransactions'
import {Params} from '@feathersjs/feathers'
import {AccessLevels, checkRecordAccess, hasAccountAccess} from '../../hooks/accessControl'

export class WalletBalance extends Service {
  app: Application

  constructor(options: Partial<MongooseServiceOptions>, app: Application) {
    super(options)
    this.app = app
  }

  // Get balance for a specific user/school and balance type
  async getBalance({uid, balanceType}: any, params: Params): Promise<any> {
    const accountId = uid || params.user?._id
    if (params.provider) {
      await hasAccountAccess(params.user?._id, accountId, this.app, ['subjectCoordinator', 'admin'])
    }

    const balance = await this.Model.findOne({uid: accountId, balanceType}).lean()
    if (!balance) {
      return {
        uid,
        balanceType,
        availableBalance: 0,
        reservedBalance: 0,
        totalBalance: 0,
        version: 0,
      }
    }

    return balance
  }

  // Update balance (add or subtract)
  async updateBalance(uid: string, isSchool: boolean = false, balanceType: string, amount: number, transactionId?: string, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)
    const options = Acan.getTxnOptions(params)

    if (numericAmount === 0) {
      // simply find the balance
      return await this.Model.findOne({uid, balanceType, isSchool}).lean()
    }

    // For negative amounts, ensure sufficient balance
    const query =
      numericAmount < 0
        ? {
            uid,
            balanceType,
            isSchool,
            $expr: {$gte: [{$add: [{$ifNull: ['$availableBalance', 0]}, numericAmount]}, 0]},
          }
        : {uid, isSchool, balanceType}

    const updateData: any = {
      $inc: {
        availableBalance: numericAmount,
        version: 1,
      },
      $set: {
        lastTransactionDate: new Date(),
        ...(transactionId && {lastTransactionId: transactionId}),
      },
    }

    const updateOptions: any = {
      new: true,
      upsert: numericAmount >= 0, // Only create new records for positive amounts
      ...options,
    }

    const updatedBalance = await this.Model.findOneAndUpdate(query, updateData, updateOptions)

    if (!updatedBalance) {
      if (numericAmount < 0) {
        throw new BadRequest('Insufficient balance')
      } else {
        throw new NotFound('Balance record not found')
      }
    }

    return updatedBalance
  }

  // Reserve balance (move from available to reserved)
  async reserveBalance(uid: string, isSchool: boolean, balanceType: string, amount: number, transactionId?: string, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)
    const options = Acan.getTxnOptions(params)

    if (numericAmount <= 0) {
      throw new BadRequest('Reserve amount must be positive')
    }

    const query = {
      uid,
      balanceType,
      isSchool,
      $expr: {$gte: [{$ifNull: ['$availableBalance', 0]}, numericAmount]},
    }

    const updateData = {
      $inc: {
        availableBalance: numericAmount * -1,
        reservedBalance: numericAmount,
        version: 1,
      },
      $set: {
        lastTransactionDate: new Date(),
        ...(transactionId && {lastTransactionId: transactionId}),
      },
    }

    const updateOptions: any = {
      new: true,
      upsert: false,
      ...options,
    }
    console.log('reserveBalance', query)
    const updatedBalance = await this.Model.findOneAndUpdate(query, updateData, updateOptions)

    if (!updatedBalance) {
      throw new BadRequest('Insufficient available balance to reserve')
    }

    return updatedBalance
  }

  // Release reserved balance (move from reserved back to available)
  async releaseReservedBalance(uid: string, isSchool: boolean, balanceType: string, amount: number, transactionId?: string, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)
    const options = Acan.getTxnOptions(params)

    if (numericAmount <= 0) {
      throw new BadRequest('Release amount must be positive')
    }

    const query = {
      uid,
      balanceType,
      isSchool,
      $expr: {$gte: [{$ifNull: ['$reservedBalance', 0]}, numericAmount]},
    }

    const updateData = {
      $inc: {
        availableBalance: numericAmount,
        reservedBalance: numericAmount * -1,
        version: 1,
      },
      $set: {
        lastTransactionDate: new Date(),
        ...(transactionId && {lastTransactionId: transactionId}),
      },
    }

    const updateOptions: any = {
      new: true,
      upsert: false,
      ...options,
    }
    console.log('releaseReservedBalance', !!options.session)
    const updatedBalance = await this.Model.findOneAndUpdate(query, updateData, updateOptions)

    if (!updatedBalance) {
      throw new BadRequest('Insufficient reserved balance to release')
    }

    return updatedBalance
  }

  // Deduct from reserved balance (when payment is confirmed)
  async deductReservedBalance(uid: string, isSchool: boolean, balanceType: string, amount: number, params?: TxnParams): Promise<any> {
    const numericAmount = Number(amount)
    const options = Acan.getTxnOptions(params)

    if (numericAmount <= 0) {
      throw new BadRequest('Deduct amount must be positive')
    }

    const query = {
      uid,
      balanceType,
      isSchool,
      $expr: {$gte: [{$ifNull: ['$reservedBalance', 0]}, numericAmount]},
    }

    const updateData = {
      $inc: {
        reservedBalance: numericAmount * -1,
        version: 1,
      },
      $set: {
        lastTransactionDate: new Date(),
        lastTransactionId: '',
      },
    }

    const updateOptions: any = {
      new: true,
      upsert: false,
      ...options,
    }
    console.log('deductReservedBalance', JSON.stringify(query))
    const updatedBalance = await this.Model.findOneAndUpdate(query, updateData, updateOptions)

    if (!updatedBalance) {
      throw new BadRequest('Insufficient reserved balance to deduct')
    }

    return updatedBalance
  }
}
