import * as authentication from '@feathersjs/authentication'
import hook from '../../hook'
import {HookContext} from '@feathersjs/feathers'
// Don't remove this comment. It's needed to format import lines nicely.

const {authenticate} = authentication.hooks

export default {
  before: {
    all: [authenticate('jwt')],
    find: [hook.disallowExternal],
    get: [
      hook.toClass,
      (d: HookContext) => {
        if (hook.classExist(d)) return d
        hook.disallowExternal(d)
      },
    ],
    create: [hook.disallowExternal],
    update: [hook.disallowExternal],
    patch: [hook.disallowExternal],
    remove: [hook.disallowExternal],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
