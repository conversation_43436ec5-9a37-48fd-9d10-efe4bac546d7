// Initializes the `wallet-balance` service on path `/wallet-balance`
import {ServiceAddons} from '@feathersjs/feathers'
import {Application} from '../../declarations'
import {WalletBalance} from './wallet-balance.class'
import createModel from '../../models/wallet-balance.model'
import hooks from './wallet-balance.hooks'

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    'wallet-balance': WalletBalance & ServiceAddons<any>
  }
}

export default function (app: Application): void {
  const options = {
    Model: createModel(app),
    paginate: app.get('paginate'),
  }

  // Initialize our service with any options it requires
  app.use('/wallet-balance', new WalletBalance(options, app))

  // Get our initialized service so that we can register hooks
  const service = app.service('wallet-balance')

  service.hooks(hooks)
}
