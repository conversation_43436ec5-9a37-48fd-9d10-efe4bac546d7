import {HookContext} from '@feathersjs/feathers'
import * as authentication from '@feathersjs/authentication'
// Don't remove this comment. It's needed to format import lines nicely.

import hook from '../../hook'
import {accessControl} from '../../hooks/accessControl'
const {authenticate} = authentication.hooks

export default {
  before: {
    all: [
      authenticate('jwt'),
      accessControl({
        schoolAccess: ['admin', 'subjectCoordinator'],
      }),
    ],
    find: [],
    get: [hook.toClass],
    create: [hook.disallowExternal],
    update: [hook.disallowExternal],
    patch: [hook.disallowExternal],
    remove: [hook.disallowExternal],
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: [],
  },
}
