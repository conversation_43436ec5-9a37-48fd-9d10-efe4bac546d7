// hooks/access-control.ts

import {HookContext, Hook} from '@feathersjs/feathers'
import {Forbidden, BadRequest} from '@feathersjs/errors'
import hook from '../hook'

export type AccessLevels = ('mainContact' | 'admin' | 'subjectCoordinator')[]
interface AccessControlOptions {
  uidField?: string
  isSchoolField?: string
  allowCreate?: boolean
  allowUpdate?: boolean
  schoolAccess: AccessLevels
  managerRoles?: string[]
}

interface User {
  _id?: string
  id?: string
  role?: string
  roles?: string[]
  [key: string]: any
}

interface QueryWithSys {
  $sys?: boolean
  [key: string]: any
}

/**
 * Universal access control hook for uid-based resources (orders, income-logs, etc.)
 * Handles both user and school contexts with admin verification
 * Includes system admin bypass logic for Sys Portal
 */
const accessControl = (options: AccessControlOptions): Hook => {
  const {uidField = 'uid', isSchoolField = 'isSchool', allowCreate = false, allowUpdate = false, schoolAccess = [], managerRoles = []} = options

  return async (context: HookContext): Promise<HookContext> => {
    if (hook.classExist(context)) return context
    const {app, method, type, params} = context
    console.log('Order_Query_1', JSON.stringify(context.params.query), !!params?.provider)
    if (!params?.provider) return context
    if (type !== 'before') return context

    const applicableMethods = ['find', 'get']
    if (allowCreate) applicableMethods.push('create')
    if (allowUpdate) applicableMethods.push('update', 'patch')

    if (!applicableMethods.includes(method)) return context

    if (!params.user) {
      throw new Forbidden('Authentication required')
    }

    const currentUser = params.user as User
    const currentUserId = currentUser._id?.toString()

    if (!currentUserId) {
      throw new Forbidden('Authentication required')
    }

    if (method === 'find' && params.query) {
      const query = params.query as QueryWithSys

      if (query.$sys) {
        const hasSystemAccess = hook.roleHas(['sys', 'admin'])(context) || hook.managerRoleHas(managerRoles)(context)
        console.log('hasSystemAccess', hasSystemAccess)
        if (!hasSystemAccess) {
          context.result = null
          return context
        }

        delete query.$sys
        return context
      }
    }

    if (method !== 'find') {
      const hasSystemAccess = hook.roleHas(['sys', 'admin'])(context) || hook.managerRoleHas(managerRoles)(context)
      if (hasSystemAccess) return context
    }

    switch (method) {
      case 'find':
        return handleFind(context, currentUserId, uidField, schoolAccess, app)
      case 'get':
        return handleGet(context, currentUserId, uidField, isSchoolField, schoolAccess, app)
      case 'create':
        return handleCreate(context, currentUserId, uidField, isSchoolField, schoolAccess, app)
      case 'update':
      case 'patch':
        return handleUpdate(context, currentUserId, uidField, isSchoolField, schoolAccess, app)
      default:
        return context
    }
  }
}

const handleFind = async (context: HookContext, currentUserId: string, uidField: string, schoolAccess: AccessLevels, app: any): Promise<HookContext> => {
  const {params} = context
  const query = params.query || {}

  if (query[uidField]) {
    if (query[uidField] === currentUserId) {
      return context
    }

    const hasAccess = await hasSchoolAccess(currentUserId, query[uidField], schoolAccess, app)
    if (hasAccess) {
      return context
    }
  }

  query[uidField] = currentUserId
  params.query = query

  return context
}

const handleGet = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  schoolAccess: AccessLevels,
  app: any
): Promise<HookContext> => {
  const {id, service} = context

  try {
    const record = await service._get(id, {provider: null})
    console.log('record', record?.buyer)

    if (!record) {
      throw new BadRequest('Record not found')
    }

    const hasAccess = await checkRecordAccess(record, currentUserId, app, schoolAccess, uidField, isSchoolField)

    if (!hasAccess) {
      throw new Forbidden('Access denied to this record')
    }

    return context
  } catch (error: any) {
    if (error.name === 'Forbidden' || error.name === 'BadRequest') {
      throw error
    }
    throw new BadRequest('Unable to verify access')
  }
}

const handleCreate = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  schoolAccess: AccessLevels,
  app: any
): Promise<HookContext> => {
  const {data} = context
  const records = Array.isArray(data) ? data : [data]

  for (const record of records) {
    const hasAccess = await checkCreateAccess(record, currentUserId, uidField, isSchoolField, schoolAccess, app)

    if (!hasAccess) {
      throw new Forbidden('Access denied to create this record')
    }
  }

  return context
}

const handleUpdate = async (
  context: HookContext,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  schoolAccess: AccessLevels,
  app: any
): Promise<HookContext> => {
  const {id, service} = context

  try {
    const existingRecord = await service._get(id, {provider: null})

    const hasAccess = await checkRecordAccess(existingRecord, currentUserId, app, schoolAccess, uidField, isSchoolField)

    if (!hasAccess) {
      throw new Forbidden('Access denied to update this record')
    }

    return context
  } catch (error: any) {
    if (error.name === 'Forbidden') {
      throw error
    }
    throw new BadRequest('Unable to verify update access')
  }
}

const checkRecordAccess = async (
  record: any,
  currentUserId: string,
  app: any,
  schoolAccess: AccessLevels,
  uidField: string = 'uid',
  isSchoolField: string = 'isSchool'
): Promise<boolean> => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  return await hasSchoolAccess(currentUserId, recordUid, schoolAccess, app)
}

const checkCreateAccess = async (
  record: any,
  currentUserId: string,
  uidField: string,
  isSchoolField: string,
  schoolAccess: AccessLevels,
  app: any
): Promise<boolean> => {
  const recordUid = record[uidField]
  const recordIsSchool = record[isSchoolField]

  if (!recordIsSchool) {
    return recordUid === currentUserId
  }

  return await hasSchoolAccess(currentUserId, recordUid, schoolAccess, app)
}

const hasAccountAccess = async (userId: string, accountId: string, app: any, schoolAccess: AccessLevels): Promise<boolean> => {
  let hasAccess = false
  if (userId === accountId) {
    hasAccess = true
  } else {
    hasAccess = await hasSchoolAccess(userId, accountId, schoolAccess, app)
  }

  if (!hasAccess) {
    throw new Forbidden("You don't have permission to view this information.")
  }

  return hasAccess
}

const hasSchoolAccess = async (userId: string, schoolId: string, schoolAccess: AccessLevels, app: any): Promise<boolean> => {
  try {
    if (schoolAccess.includes('mainContact')) {
      const schoolPlanService = app.service('school-plan')
      const schoolPlan = await schoolPlanService.Model.findOne({
        _id: schoolId,
        contact: userId,
      })

      if (schoolPlan) {
        return true
      }
    }

    if (schoolAccess.includes('admin')) {
      const schoolUserService = app.service('school-user')
      const schoolUserRecord = await schoolUserService.Model.findOne({
        uid: userId,
        school: schoolId,
        status: 2,
        role: 'admin',
        del: {$ne: true},
      })

      if (schoolUserRecord) {
        return true
      }
    }

    if (schoolAccess.includes('subjectCoordinator')) {
      const subjectsService = app.service('subjects')
      const subjectRecord = await subjectsService.Model.findOne({
        uid: schoolId,
        coordinator: userId,
        del: {$ne: true},
      })

      if (subjectRecord) {
        return true
      }
    }

    return false
  } catch (error) {
    return false
  }
}

export {accessControl, checkRecordAccess, hasSchoolAccess, hasAccountAccess}
